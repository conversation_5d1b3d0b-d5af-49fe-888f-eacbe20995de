{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Profile Settings{% endblock %}
{% block page_title %}Profile Settings{% endblock %}
{% block page_description %}Manage your personal information and account settings{% endblock %}

{% block extra_css %}
<!-- Preload critical fonts -->
<link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Raleway:wght@300;400;500;600&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Raleway:wght@300;400;500;600&display=swap"></noscript>

<!-- Critical CSS with media queries for performance -->
<link rel="stylesheet" href="{% static 'css/profile-forms.css' %}" media="screen">
<link rel="stylesheet" href="{% static 'css/global-animations.css' %}" media="screen">
<link rel="stylesheet" href="{% static 'css/enhanced-inputs.css' %}" media="screen">
<link rel="stylesheet" href="{% static 'css/enhanced-profile.css' %}" media="screen">

<!-- Cropper.js CSS for image cropping functionality -->
<link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css"></noscript>
{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 font-raleway">Profile</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
<!-- Enhanced Profile Hero Section with Improved Layout -->
<div class="profile-hero-wrapper mb-8 animate-fade-in-up">
    <div class="profile-hero-container relative overflow-hidden rounded-3xl min-h-[500px] lg:min-h-[600px]">
        <!-- Dynamic Background Pattern with Enhanced Gradients -->
        <div class="absolute inset-0 bg-gradient-to-br from-harrier-red/90 via-harrier-dark/95 to-harrier-blue/90"></div>

        <!-- Enhanced Animated Background Elements -->
        <div class="absolute inset-0 opacity-15">
            <div class="absolute top-0 right-0 w-96 h-96 bg-white rounded-full blur-3xl transform translate-x-48 -translate-y-48 animate-pulse"></div>
            <div class="absolute bottom-0 left-0 w-80 h-80 bg-harrier-blue rounded-full blur-3xl transform -translate-x-40 translate-y-40 animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/2 left-1/2 w-64 h-64 bg-white rounded-full blur-2xl transform -translate-x-32 -translate-y-32 animate-pulse" style="animation-delay: 2s;"></div>
            <div class="absolute top-1/4 right-1/4 w-48 h-48 bg-harrier-red rounded-full blur-2xl transform translate-x-24 -translate-y-24 animate-pulse" style="animation-delay: 3s;"></div>
        </div>

        <!-- Enhanced Glassmorphism Overlay -->
        <div class="absolute inset-0 backdrop-blur-sm bg-gradient-to-r from-white/8 via-transparent to-white/8"></div>

        <!-- Hero Content with Improved Layout -->
        <div class="relative z-10 p-6 lg:p-12 h-full flex flex-col justify-between">
            <!-- Top Section: Profile Information -->
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-8 mb-8">
                <!-- Profile Avatar and Basic Info -->
                <div class="flex flex-col sm:flex-row items-start sm:items-center gap-6 lg:gap-8 flex-1">
                    <!-- Enhanced Profile Picture Container -->
                    <div class="relative profile-avatar-section group cursor-pointer transform transition-all duration-300 hover:scale-105" onclick="document.getElementById('profilePictureInput').click()">
                        <div class="profile-avatar-container relative w-36 h-36 lg:w-44 lg:h-44">
                            {% if user.profile_picture %}
                                <img class="profile-avatar w-full h-full object-cover rounded-full shadow-2xl border-4 border-white/40 transition-all duration-300 group-hover:border-white/60 group-hover:shadow-3xl"
                                     src="{{ user.profile_picture.url }}"
                                     alt="{{ user.get_full_name|default:user.username }}"
                                     id="profilePreview">
                            {% else %}
                                <div class="profile-avatar w-full h-full bg-white/25 backdrop-blur-sm rounded-full shadow-2xl border-4 border-white/40 flex items-center justify-center text-white font-bold text-4xl lg:text-5xl font-montserrat transition-all duration-300 group-hover:border-white/60 group-hover:shadow-3xl group-hover:bg-white/30" id="profilePreview">
                                    {{ user.first_name|first|default:user.username|first|upper }}
                                </div>
                            {% endif %}

                            <!-- Enhanced Profile Picture Edit Overlay -->
                            <div class="profile-edit-overlay absolute inset-0 rounded-full bg-black/70 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 backdrop-blur-sm">
                                <div class="profile-edit-content text-center transform scale-90 group-hover:scale-100 transition-transform duration-300">
                                    <i class="fas fa-camera text-white text-2xl mb-2"></i>
                                    <div class="text-white text-sm font-medium font-raleway">Change Photo</div>
                                </div>
                            </div>

                            <!-- Enhanced Status Indicators -->
                            <div class="absolute -bottom-2 -right-2 flex flex-col gap-1">
                                <!-- Online Status -->
                                <div class="status-indicator w-7 h-7 bg-green-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center animate-pulse">
                                    <div class="w-2.5 h-2.5 bg-white rounded-full"></div>
                                </div>
                                {% if user.is_email_verified %}
                                    <!-- Verified Badge -->
                                    <div class="status-indicator w-7 h-7 bg-blue-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                {% endif %}
                                {% if user.role == 'vendor' and vendor.is_approved %}
                                    <!-- Business Verified Badge -->
                                    <div class="status-indicator w-7 h-7 bg-purple-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center">
                                        <i class="fas fa-shield-check text-white text-sm"></i>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Enhanced Profile Completion Ring -->
                        <div class="absolute -inset-4 rounded-full">
                            <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="46" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="2"/>
                                <circle cx="50" cy="50" r="46" fill="none" stroke="rgba(255,255,255,0.95)" stroke-width="2.5"
                                        stroke-dasharray="289" stroke-dashoffset="115" stroke-linecap="round"
                                        class="transition-all duration-1000 ease-out"
                                        id="profileCompletionRing"/>
                            </svg>
                            <div class="absolute -bottom-10 left-1/2 transform -translate-x-1/2">
                                <span class="text-white text-sm font-bold bg-black/40 rounded-full px-3 py-1.5 backdrop-blur-sm border border-white/30 font-montserrat" id="profileCompletionText">60%</span>
                            </div>
                        </div>

                        <!-- Hidden File Input -->
                        <input type="file"
                               name="profile_picture"
                               id="profilePictureInput"
                               accept="image/jpeg,image/jpg,image/png,image/webp"
                               class="hidden"
                               onchange="previewProfileImage(this)">
                    </div>

                    <!-- Enhanced User Information -->
                    <div class="text-white space-y-5 flex-1">
                        <!-- Name and Title with Better Typography -->
                        <div class="space-y-4">
                            <h1 class="text-4xl lg:text-6xl font-bold font-montserrat bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent leading-tight tracking-tight">
                                {{ user.first_name|default:"" }} {{ user.last_name|default:"" }}
                                {% if not user.first_name and not user.last_name %}
                                    {{ user.username }}
                                {% endif %}
                            </h1>

                            <!-- Enhanced Role and Status Badges -->
                            <div class="flex flex-wrap items-center gap-3">
                                <span class="role-badge role-badge-{{ user.role }} px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white font-semibold text-sm border border-white/30 font-raleway">
                                    <i class="fas fa-user-tag mr-2"></i>
                                    {{ user.get_role_display }}
                                </span>

                                {% if user.is_email_verified %}
                                    <span class="status-badge status-verified px-3 py-1.5 bg-green-500/80 backdrop-blur-sm rounded-full text-white font-medium text-xs border border-green-400/50 font-raleway">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Email Verified
                                    </span>
                                {% else %}
                                    <span class="status-badge status-pending px-3 py-1.5 bg-orange-500/80 backdrop-blur-sm rounded-full text-white font-medium text-xs border border-orange-400/50 font-raleway">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        Verify Email
                                    </span>
                                {% endif %}

                                {% if user.role == 'vendor' and vendor %}
                                    {% if vendor.is_approved %}
                                        <span class="status-badge status-business-verified px-3 py-1.5 bg-purple-500/80 backdrop-blur-sm rounded-full text-white font-medium text-xs border border-purple-400/50 font-raleway">
                                            <i class="fas fa-shield-alt mr-1"></i>
                                            Verified Business
                                        </span>
                                    {% else %}
                                        <span class="status-badge status-pending px-3 py-1.5 bg-yellow-500/80 backdrop-blur-sm rounded-full text-white font-medium text-xs border border-yellow-400/50 font-raleway">
                                            <i class="fas fa-clock mr-1"></i>
                                            Pending Approval
                                        </span>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>

                        <!-- Enhanced Company Information (for vendors) -->
                        {% if user.role == 'vendor' and vendor %}
                            <div class="vendor-info space-y-3 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                                <h3 class="text-2xl font-semibold text-blue-100 font-raleway">{{ vendor.company_name }}</h3>
                                <div class="flex flex-wrap items-center gap-4 text-sm">
                                    {% if vendor.business_type %}
                                        <div class="flex items-center text-blue-200">
                                            <i class="fas fa-building mr-2"></i>
                                            <span>{{ vendor.business_type }}</span>
                                        </div>
                                    {% endif %}
                                    {% if vendor.year_established %}
                                        <div class="flex items-center text-blue-200">
                                            <i class="fas fa-calendar mr-2"></i>
                                            <span>Established {{ vendor.year_established }}</span>
                                        </div>
                                    {% endif %}
                                    {% if vendor.number_of_employees %}
                                        <div class="flex items-center text-blue-200">
                                            <i class="fas fa-users mr-2"></i>
                                            <span>{{ vendor.number_of_employees }} employees</span>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Enhanced Bio Section -->
                        {% if user.bio %}
                            <div class="bio-section p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                                <p class="text-blue-100 text-lg leading-relaxed font-raleway">
                                    {{ user.bio|truncatewords:30 }}
                                </p>
                            </div>
                        {% endif %}

                        <!-- Enhanced Contact Information -->
                        <div class="contact-info grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                            {% if user.email %}
                                <div class="contact-item flex items-center p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 cursor-pointer" title="Click to send email" onclick="handleContactClick('email', '{{ user.email }}')"
                                    <i class="fas fa-envelope mr-3 text-blue-300 text-lg"></i>
                                    <span class="text-blue-100 font-raleway">{{ user.email }}</span>
                                </div>
                            {% endif %}
                            {% if user.phone %}
                                <div class="contact-item flex items-center p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 cursor-pointer" title="Click to copy phone number" onclick="handleContactClick('phone', '{{ user.phone }}')"
                                    <i class="fas fa-phone mr-3 text-blue-300 text-lg"></i>
                                    <span class="text-blue-100 font-raleway">{{ user.phone }}</span>
                                </div>
                            {% endif %}
                            {% if user.city %}
                                <div class="contact-item flex items-center p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 cursor-pointer" title="Click to view on map" onclick="handleContactClick('location', '{{ user.city }}, {{ user.country }}')"
                                    <i class="fas fa-map-marker-alt mr-3 text-blue-300 text-lg"></i>
                                    <span class="text-blue-100 font-raleway">{{ user.city }}, {{ user.country }}</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

            </div>

            <!-- Bottom Section: Enhanced Stats and Actions -->
            <div class="profile-actions-section flex flex-col lg:flex-row items-start lg:items-end justify-between gap-8">
                <!-- Enhanced Quick Stats Cards -->
                <div class="stats-grid grid grid-cols-2 lg:grid-cols-4 gap-4 flex-1">
                    <!-- Profile Completion -->
                    <div class="stat-card interactive-element glassmorphism p-4 bg-white/15 backdrop-blur-md rounded-xl border border-white/30 hover:bg-white/20 transition-all duration-300 group cursor-pointer" title="Click to see incomplete fields" onclick="handleStatClick('complete')"
                        <div class="stat-content flex items-center gap-3">
                            <div class="stat-icon w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-user-check text-white text-lg"></i>
                            </div>
                            <div class="stat-details">
                                <p class="stat-value text-white font-bold text-2xl font-montserrat" id="heroCompletionValue">60%</p>
                                <p class="stat-label text-blue-100 text-sm font-raleway">Complete</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member Since -->
                    <div class="stat-card interactive-element glassmorphism p-4 bg-white/15 backdrop-blur-md rounded-xl border border-white/30 hover:bg-white/20 transition-all duration-300 group cursor-pointer" title="Member information" onclick="handleStatClick('member since')"
                        <div class="stat-content flex items-center gap-3">
                            <div class="stat-icon w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-calendar text-white text-lg"></i>
                            </div>
                            <div class="stat-details">
                                <p class="stat-value text-white font-bold text-lg font-montserrat">{{ user.date_joined|date:"M Y" }}</p>
                                <p class="stat-label text-blue-100 text-sm font-raleway">Member Since</p>
                            </div>
                        </div>
                    </div>

                    <!-- Role-specific Stats -->
                    {% if user.role == 'vendor' and vendor %}
                        <div class="stat-card glassmorphism p-4 bg-white/15 backdrop-blur-md rounded-xl border border-white/30 hover:bg-white/20 transition-all duration-300 group">
                            <div class="stat-content flex items-center gap-3">
                                <div class="stat-icon w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-car text-white text-lg"></i>
                                </div>
                                <div class="stat-details">
                                    <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ vendor.total_cars|default:0 }}</p>
                                    <p class="stat-label text-blue-100 text-sm font-raleway">Listings</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card glassmorphism p-4 bg-white/15 backdrop-blur-md rounded-xl border border-white/30 hover:bg-white/20 transition-all duration-300 group">
                            <div class="stat-content flex items-center gap-3">
                                <div class="stat-icon w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-eye text-white text-lg"></i>
                                </div>
                                <div class="stat-details">
                                    <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ vendor.total_profile_views|default:0 }}</p>
                                    <p class="stat-label text-blue-100 text-sm font-raleway">Profile Views</p>
                                </div>
                            </div>
                        </div>
                    {% elif user.role == 'customer' %}
                        <div class="stat-card glassmorphism p-4 bg-white/15 backdrop-blur-md rounded-xl border border-white/30 hover:bg-white/20 transition-all duration-300 group">
                            <div class="stat-content flex items-center gap-3">
                                <div class="stat-icon w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-shopping-cart text-white text-lg"></i>
                                </div>
                                <div class="stat-details">
                                    <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ user.orders.count|default:0 }}</p>
                                    <p class="stat-label text-blue-100 text-sm font-raleway">Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card glassmorphism p-4 bg-white/15 backdrop-blur-md rounded-xl border border-white/30 hover:bg-white/20 transition-all duration-300 group">
                            <div class="stat-content flex items-center gap-3">
                                <div class="stat-icon w-12 h-12 bg-gradient-to-br from-pink-400 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-heart text-white text-lg"></i>
                                </div>
                                <div class="stat-details">
                                    <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ user.wishlist_items.count|default:0 }}</p>
                                    <p class="stat-label text-blue-100 text-sm font-raleway">Wishlist</p>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="stat-card glassmorphism p-4 bg-white/15 backdrop-blur-md rounded-xl border border-white/30 hover:bg-white/20 transition-all duration-300 group">
                            <div class="stat-content flex items-center gap-3">
                                <div class="stat-icon w-12 h-12 bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-users text-white text-lg"></i>
                                </div>
                                <div class="stat-details">
                                    <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ total_users|default:0 }}</p>
                                    <p class="stat-label text-blue-100 text-sm font-raleway">Total Users</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card glassmorphism p-4 bg-white/15 backdrop-blur-md rounded-xl border border-white/30 hover:bg-white/20 transition-all duration-300 group">
                            <div class="stat-content flex items-center gap-3">
                                <div class="stat-icon w-12 h-12 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-chart-line text-white text-lg"></i>
                                </div>
                                <div class="stat-details">
                                    <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ total_cars|default:0 }}</p>
                                    <p class="stat-label text-blue-100 text-sm font-raleway">Total Cars</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Enhanced Action Buttons -->
                <div class="action-buttons flex flex-col lg:flex-row gap-3 lg:gap-4">
                    <button type="button" class="action-btn action-btn-primary px-6 py-3 bg-gradient-to-r from-harrier-red to-harrier-red-dark text-white rounded-xl font-semibold font-montserrat hover:from-harrier-red-dark hover:to-harrier-red transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center" onclick="scrollToSection('personal-tab')">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Profile
                    </button>

                    {% if user.role == 'vendor' %}
                        <a href="{% url 'core:vendor_profile' %}" class="action-btn action-btn-secondary px-6 py-3 bg-white/20 backdrop-blur-sm text-white rounded-xl font-semibold font-montserrat border border-white/30 hover:bg-white/30 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center">
                            <i class="fas fa-building mr-2"></i>
                            Business Profile
                        </a>
                    {% endif %}

                    <button type="button" class="action-btn action-btn-outline px-6 py-3 bg-transparent text-white rounded-xl font-semibold font-montserrat border-2 border-white/50 hover:bg-white/10 hover:border-white/70 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center" onclick="scrollToSection('security-tab')">
                        <i class="fas fa-shield-alt mr-2"></i>
                        Security
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Completion Banner -->
<div class="profile-completion-banner mb-8 animate-fade-in-up" style="animation-delay: 0.2s;">
    <div class="completion-card glassmorphism bg-gradient-to-r from-harrier-blue/10 to-harrier-red/10 border border-white/20">
        <div class="flex items-center justify-between p-6">
            <div class="flex items-center gap-4">
                <div class="completion-icon">
                    <i class="fas fa-chart-line text-harrier-blue text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-harrier-dark font-montserrat">Complete Your Profile</h3>
                    <p class="text-gray-600 text-sm font-raleway">Add more information to improve your profile visibility and credibility</p>
                </div>
            </div>
            <div class="completion-progress">
                <div class="progress-circle" data-progress="60">
                    <svg class="progress-ring" width="60" height="60">
                        <circle class="progress-ring-circle" stroke="#e5e7eb" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
                        <circle class="progress-ring-circle progress-ring-fill" stroke="#DC2626" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
                    </svg>
                    <div class="progress-text">
                        <span class="text-sm font-bold text-harrier-dark">60%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="completion-actions border-t border-white/20 p-4">
            <div class="flex flex-wrap gap-2">
                {% if not user.profile_picture %}
                    <span class="completion-task">
                        <i class="fas fa-camera mr-1"></i>Add Photo
                    </span>
                {% endif %}
                {% if not user.bio %}
                    <span class="completion-task">
                        <i class="fas fa-edit mr-1"></i>Add Bio
                    </span>
                {% endif %}
                {% if not user.phone %}
                    <span class="completion-task">
                        <i class="fas fa-phone mr-1"></i>Add Phone
                    </span>
                {% endif %}
                {% if user.role == 'vendor' and vendor and not vendor.company_logo %}
                    <span class="completion-task">
                        <i class="fas fa-building mr-1"></i>Add Logo
                    </span>
                {% endif %}
            </div>
        </div>
    </div>
</div>
<!-- Enhanced Profile Navigation with Modern Design -->
<div class="profile-navigation-wrapper mb-8 animate-fade-in-up" style="animation-delay: 0.3s;">
    <div class="navigation-container glassmorphism bg-white/80 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
        <!-- Navigation Header -->
        <div class="nav-header bg-gradient-to-r from-harrier-red/5 to-harrier-blue/5 p-6 border-b border-white/20">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat">Profile Settings</h2>
                    <p class="text-gray-600 text-sm font-raleway">Manage your account information and preferences</p>
                </div>
                <div class="nav-actions flex gap-2">
                    <button type="button" class="nav-action-btn" onclick="saveProfile()" id="saveProfileBtn">
                        <i class="fas fa-save mr-2"></i>
                        <span class="save-btn-text">Save Changes</span>
                    </button>
                    <button type="button" class="nav-action-btn nav-action-secondary" onclick="resetForm()">
                        <i class="fas fa-undo mr-2"></i>Reset
                    </button>
                    <div class="auto-save-indicator" id="autoSaveIndicator">
                        <i class="fas fa-check-circle text-green-500 mr-1"></i>
                        <span class="text-sm text-green-600">Auto-saved</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Tab Navigation with Improved Visibility -->
        <div class="tab-navigation-container mb-8">
            <!-- Desktop Tab Navigation -->
            <nav class="tab-navigation hidden lg:flex" role="tablist">
                <!-- Personal Information Tab -->
                <button type="button"
                        class="nav-tab active"
                        data-tab="personal"
                        role="tab"
                        aria-selected="true"
                        aria-controls="personal-tab">
                    <div class="tab-content flex items-center gap-3 p-4">
                        <div class="tab-icon w-10 h-10 bg-harrier-red/20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user text-harrier-red"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title block text-sm font-semibold text-harrier-dark font-montserrat">Personal</span>
                            <span class="tab-subtitle block text-xs text-gray-600 font-raleway">Basic information</span>
                        </div>
                    </div>
                    <div class="tab-indicator absolute bottom-0 left-0 w-full h-1 bg-harrier-red rounded-t-lg"></div>
                </button>

                <!-- Contact Information Tab -->
                <button type="button"
                        class="nav-tab"
                        data-tab="contact"
                        role="tab"
                        aria-selected="false"
                        aria-controls="contact-tab">
                    <div class="tab-content flex items-center gap-3 p-4">
                        <div class="tab-icon w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-address-book text-gray-600"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title block text-sm font-semibold text-gray-700 font-montserrat">Contact</span>
                            <span class="tab-subtitle block text-xs text-gray-500 font-raleway">Communication details</span>
                        </div>
                    </div>
                    <div class="tab-indicator absolute bottom-0 left-0 w-full h-1 bg-transparent"></div>
                </button>

                <!-- Preferences Tab -->
                <button type="button"
                        class="nav-tab"
                        data-tab="preferences"
                        role="tab"
                        aria-selected="false"
                        aria-controls="preferences-tab">
                    <div class="tab-content flex items-center gap-3 p-4">
                        <div class="tab-icon w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cog text-gray-600"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title block text-sm font-semibold text-gray-700 font-montserrat">Preferences</span>
                            <span class="tab-subtitle block text-xs text-gray-500 font-raleway">Account settings</span>
                        </div>
                    </div>
                    <div class="tab-indicator absolute bottom-0 left-0 w-full h-1 bg-transparent"></div>
                </button>

                <!-- Security Tab -->
                <button type="button"
                        class="nav-tab"
                        data-tab="security"
                        role="tab"
                        aria-selected="false"
                        aria-controls="security-tab">
                    <div class="tab-content flex items-center gap-3 p-4">
                        <div class="tab-icon w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-gray-600"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title block text-sm font-semibold text-gray-700 font-montserrat">Security</span>
                            <span class="tab-subtitle block text-xs text-gray-500 font-raleway">Password & privacy</span>
                        </div>
                    </div>
                    <div class="tab-indicator absolute bottom-0 left-0 w-full h-1 bg-transparent"></div>
                </button>

                <!-- Business Tab (for vendors only) -->
                {% if user.role == 'vendor' %}
                <button type="button"
                        class="nav-tab"
                        data-tab="business"
                        role="tab"
                        aria-selected="false"
                        aria-controls="business-tab">
                    <div class="tab-content flex items-center gap-3 p-4">
                        <div class="tab-icon w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-building text-gray-600"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title block text-sm font-semibold text-gray-700 font-montserrat">Business</span>
                            <span class="tab-subtitle block text-xs text-gray-500 font-raleway">Company details</span>
                        </div>
                    </div>
                    <div class="tab-indicator absolute bottom-0 left-0 w-full h-1 bg-transparent"></div>
                </button>
                {% endif %}

                <!-- Analytics Tab (for vendors and admins) -->
                {% if user.role == 'vendor' or user.role == 'admin' %}
                <button type="button"
                        class="nav-tab"
                        data-tab="analytics"
                        role="tab"
                        aria-selected="false"
                        aria-controls="analytics-tab">
                    <div class="tab-content flex items-center gap-3 p-4">
                        <div class="tab-icon w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-gray-600"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title block text-sm font-semibold text-gray-700 font-montserrat">Analytics</span>
                            <span class="tab-subtitle block text-xs text-gray-500 font-raleway">Performance data</span>
                        </div>
                    </div>
                    <div class="tab-indicator absolute bottom-0 left-0 w-full h-1 bg-transparent"></div>
                </button>
                {% endif %}
            </nav>

            <!-- Enhanced Mobile Tab Selector -->
            <div class="mobile-tab-selector lg:hidden">
                <div class="mobile-tab-container bg-white/90 backdrop-blur-lg rounded-xl border border-gray-200 p-4">
                    <label for="mobileTabSelect" class="mobile-tab-label flex items-center mb-3">
                        <i class="fas fa-list mr-2 text-harrier-red"></i>
                        <span class="font-semibold text-harrier-dark font-montserrat">Select Section</span>
                    </label>
                    <select id="mobileTabSelect" class="mobile-tab-select w-full p-3 border-2 border-gray-200 rounded-lg bg-white text-harrier-dark font-raleway focus:ring-4 focus:ring-harrier-red/20 focus:border-harrier-red transition-all duration-300 shadow-sm" onchange="switchToTab(this.value)">
                        <option value="personal">📋 Personal Information</option>
                        <option value="contact">📞 Contact Details</option>
                        <option value="preferences">⚙️ Preferences</option>
                        <option value="security">🔒 Security Settings</option>
                        {% if user.role == 'vendor' %}
                            <option value="business">🏢 Business Information</option>
                        {% endif %}
                        {% if user.role == 'vendor' or user.role == 'admin' %}
                            <option value="analytics">📊 Analytics</option>
                        {% endif %}
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Enhanced Profile Form Container -->
<div class="profile-form-wrapper animate-fade-in-up" style="animation-delay: 0.4s;">
    <!-- HTMX Messages Area -->
    <div id="profile-messages" class="mb-6"></div>

    <!-- HTMX Loading Indicator -->
    <div id="profile-loading" class="htmx-indicator">
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="loading-container glassmorphism bg-white/90 rounded-2xl p-8 flex items-center gap-4 shadow-2xl">
                <div class="loading-spinner"></div>
                <div class="loading-text">
                    <h3 class="text-lg font-semibold text-harrier-dark font-montserrat">Saving Profile</h3>
                    <p class="text-gray-600 text-sm font-raleway">Please wait while we update your information...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Form Container -->
    <div class="form-container glassmorphism bg-white/90 backdrop-blur-lg border border-white/30 rounded-2xl overflow-hidden shadow-2xl">
        <form method="post"
              enctype="multipart/form-data"
              id="profileForm"
              class="profile-form"
              hx-post="{% url 'core:profile' %}"
              hx-trigger="submit"
              hx-target="#profile-messages"
              hx-swap="innerHTML"
              hx-indicator="#profile-loading"
              hx-on::after-request="handleFormResponse(event)">
            {% csrf_token %}

            <!-- Personal Information Tab Content -->
            <div class="tab-content active" id="personal-tab" role="tabpanel" aria-labelledby="personal-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Personal Information</h2>
                    <p class="tab-description">Update your basic profile information and personal details</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Profile Picture Section -->
                    <div class="form-section profile-picture-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-harrier-red to-harrier-red-dark">
                                <i class="fas fa-camera text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Profile Picture</h3>
                                <p class="section-subtitle">Upload a professional profile picture to personalize your account</p>
                            </div>
                        </div>

                        <div class="picture-upload-container">
                            <div class="current-picture-display">
                                <div class="picture-frame">
                                    {% if user.profile_picture %}
                                        <img class="profile-image"
                                             src="{{ user.profile_picture.url }}"
                                             alt="Current profile picture"
                                             id="profileImagePreview">
                                    {% else %}
                                        <div class="profile-placeholder" id="profileImagePreview">
                                            <i class="fas fa-user text-4xl text-gray-400"></i>
                                        </div>
                                    {% endif %}

                                    <!-- Upload Overlay -->
                                    <div class="upload-overlay">
                                        <div class="upload-content">
                                            <i class="fas fa-camera text-white text-xl mb-2"></i>
                                            <span class="text-white text-sm font-medium">Change Photo</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="upload-controls">
                                <input type="file"
                                       name="profile_picture"
                                       id="profilePictureFormInput"
                                       accept="image/jpeg,image/jpg,image/png,image/webp"
                                       class="hidden"
                                       onchange="syncProfilePictureInputs(this)">

                                <div class="upload-buttons">
                                    <button type="button"
                                            class="upload-btn upload-btn-primary"
                                            onclick="document.getElementById('profilePictureFormInput').click()">
                                        <i class="fas fa-upload mr-2"></i>
                                        Upload New Photo
                                    </button>

                                    {% if user.profile_picture %}
                                        <button type="button"
                                                class="upload-btn upload-btn-secondary"
                                                onclick="removeProfilePicture()">
                                            <i class="fas fa-trash mr-2"></i>
                                            Remove Photo
                                        </button>
                                    {% endif %}

                                    <button type="button"
                                            class="upload-btn upload-btn-outline"
                                            onclick="openImageCropper()">
                                        <i class="fas fa-crop mr-2"></i>
                                        Crop Image
                                    </button>
                                </div>

                                <div class="upload-guidelines">
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Recommended: Square image, at least 400x400px, max 5MB
                                    </p>
                                    <p class="text-xs text-gray-500 mt-1">
                                        Supported formats: JPEG, PNG, WebP
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Cover Image Section (Vendors Only) -->
                        {% if user.role == 'vendor' %}
                        <div class="cover-image-section mt-8">
                            <div class="section-header">
                                <div class="section-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                                    <i class="fas fa-image text-white"></i>
                                </div>
                                <div class="section-info">
                                    <h3 class="section-title">Cover Image</h3>
                                    <p class="section-subtitle">Add a cover image to showcase your business</p>
                                </div>
                            </div>

                            <div class="cover-upload-container">
                                <div class="cover-preview">
                                    {% if vendor.cover_image %}
                                        <img class="cover-image"
                                             src="{{ vendor.cover_image.url }}"
                                             alt="Business cover image"
                                             id="coverImagePreview">
                                    {% else %}
                                        <div class="cover-placeholder" id="coverImagePreview">
                                            <i class="fas fa-image text-6xl text-gray-400 mb-4"></i>
                                            <p class="text-gray-500 text-lg font-medium">Add a cover image</p>
                                            <p class="text-gray-400 text-sm">Showcase your business with a beautiful cover photo</p>
                                        </div>
                                    {% endif %}

                                    <!-- Cover Upload Overlay -->
                                    <div class="cover-upload-overlay">
                                        <div class="cover-upload-content">
                                            <i class="fas fa-camera text-white text-2xl mb-3"></i>
                                            <span class="text-white font-medium">Change Cover Image</span>
                                        </div>
                                    </div>
                                </div>

                                <input type="file"
                                       name="cover_image"
                                       id="coverImageInput"
                                       accept="image/jpeg,image/jpg,image/png,image/webp"
                                       class="hidden"
                                       onchange="handleImageUpload(this, 'cover')">

                                <div class="cover-controls">
                                    <button type="button"
                                            class="upload-btn upload-btn-primary"
                                            onclick="document.getElementById('coverImageInput').click()">
                                        <i class="fas fa-upload mr-2"></i>
                                        Upload Cover Image
                                    </button>

                                    {% if vendor.cover_image %}
                                        <button type="button"
                                                class="upload-btn upload-btn-secondary"
                                                onclick="removeCoverImage()">
                                            <i class="fas fa-trash mr-2"></i>
                                            Remove Cover
                                        </button>
                                    {% endif %}
                                </div>

                                <div class="cover-guidelines">
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Recommended: 1200x400px, landscape orientation, max 10MB
                                    </p>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Basic Information Section -->
                    <div class="form-section basic-info-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-harrier-blue to-harrier-blue-light">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Basic Information</h3>
                                <p class="section-subtitle">Your fundamental personal details</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- First Name -->
                            <div class="form-field">
                                <label for="first_name" class="field-label">
                                    First Name <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="first_name"
                                           id="first_name"
                                           value="{{ user.first_name }}"
                                           class="form-input"
                                           placeholder="Enter your first name"
                                           data-auto-save="true"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-user text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="first_name_feedback"></div>
                            </div>

                            <!-- Last Name -->
                            <div class="form-field">
                                <label for="last_name" class="field-label">
                                    Last Name <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="last_name"
                                           id="last_name"
                                           value="{{ user.last_name }}"
                                           class="form-input"
                                           placeholder="Enter your last name"
                                           data-auto-save="true"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-user text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="last_name_feedback"></div>
                            </div>

                            <!-- Username -->
                            <div class="form-field">
                                <label for="username" class="field-label">
                                    Username <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="username"
                                           id="username"
                                           value="{{ user.username }}"
                                           class="form-input"
                                           placeholder="Choose a unique username"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-at text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="username_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    This will be your unique identifier on the platform
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="form-field">
                                <label for="email" class="field-label">
                                    Email Address <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="email"
                                           name="email"
                                           id="email"
                                           value="{{ user.email }}"
                                           class="form-input"
                                           placeholder="Enter your email address"
                                           data-auto-save="true"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-envelope text-gray-400"></i>
                                    </div>
                                    {% if user.is_email_verified %}
                                        <div class="input-status status-verified">
                                            <i class="fas fa-check-circle text-green-500"></i>
                                        </div>
                                    {% else %}
                                        <div class="input-status status-unverified">
                                            <i class="fas fa-exclamation-circle text-orange-500"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="field-feedback" id="email_feedback"></div>
                                {% if not user.is_email_verified %}
                                    <div class="field-help text-orange-600">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        Email not verified. <a href="#" class="text-harrier-red hover:underline">Resend verification</a>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Bio and Personal Details Section -->
                    <div class="form-section bio-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-purple-500 to-purple-600">
                                <i class="fas fa-edit text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">About You</h3>
                                <p class="section-subtitle">Tell others about yourself and your interests</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Bio -->
                            <div class="form-field form-field-full">
                                <label for="bio" class="field-label">
                                    Bio / About Me
                                </label>
                                <div class="textarea-container">
                                    <textarea name="bio"
                                              id="bio"
                                              class="form-textarea"
                                              placeholder="Write a brief description about yourself, your interests, or your business..."
                                              rows="4"
                                              maxlength="500">{{ user.bio }}</textarea>
                                    <div class="textarea-counter">
                                        <span id="bio_counter">{{ user.bio|length|default:0 }}</span>/500
                                    </div>
                                </div>
                                <div class="field-feedback" id="bio_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-lightbulb mr-1"></i>
                                    A good bio helps others understand who you are and builds trust
                                </div>
                            </div>

                            <!-- Date of Birth -->
                            <div class="form-field">
                                <label for="date_of_birth" class="field-label">
                                    Date of Birth
                                </label>
                                <div class="input-container">
                                    <input type="date"
                                           name="date_of_birth"
                                           id="date_of_birth"
                                           value="{{ user.date_of_birth|date:'Y-m-d' }}"
                                           class="form-input">
                                    <div class="input-icon">
                                        <i class="fas fa-calendar text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="date_of_birth_feedback"></div>
                            </div>

                            <!-- Gender -->
                            <div class="form-field">
                                <label for="gender" class="field-label">
                                    Gender
                                </label>
                                <div class="select-container">
                                    <select name="gender" id="gender" class="form-select" data-auto-save="true">
                                        <option value="">Select Gender</option>
                                        <option value="male" {% if user.gender == 'male' %}selected{% endif %}>Male</option>
                                        <option value="female" {% if user.gender == 'female' %}selected{% endif %}>Female</option>
                                        <option value="other" {% if user.gender == 'other' %}selected{% endif %}>Other</option>
                                        <option value="prefer_not_to_say" {% if user.gender == 'prefer_not_to_say' %}selected{% endif %}>Prefer not to say</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="gender_feedback"></div>
                            </div>

                            <!-- Preferred Language -->
                            <div class="form-field">
                                <label for="preferred_language" class="field-label">
                                    Preferred Language
                                </label>
                                <div class="select-container">
                                    <select name="preferred_language" id="preferred_language" class="form-select" data-auto-save="true">
                                        <option value="en" {% if user.preferred_language == 'en' %}selected{% endif %}>English</option>
                                        <option value="sw" {% if user.preferred_language == 'sw' %}selected{% endif %}>Swahili</option>
                                        <option value="fr" {% if user.preferred_language == 'fr' %}selected{% endif %}>French</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="preferred_language_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-globe mr-1"></i>
                                    This affects the language of emails and notifications you receive
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Tab Content -->
            <div class="tab-content" id="contact-tab" role="tabpanel" aria-labelledby="contact-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Contact Information</h2>
                    <p class="tab-description">Manage your contact details and communication preferences</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Primary Contact Section -->
                    <div class="form-section contact-primary-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-green-500 to-green-600">
                                <i class="fas fa-phone text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Primary Contact</h3>
                                <p class="section-subtitle">Your main contact information</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Phone Number -->
                            <div class="form-field">
                                <label for="phone" class="field-label">
                                    Phone Number <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="tel"
                                           name="phone"
                                           id="phone"
                                           value="{{ user.phone }}"
                                           class="form-input"
                                           placeholder="+254 700 000 000"
                                           data-auto-save="true"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-phone text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="phone_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Include country code for international numbers
                                </div>
                            </div>

                            <!-- Secondary Phone -->
                            <div class="form-field">
                                <label for="secondary_phone" class="field-label">
                                    Secondary Phone
                                </label>
                                <div class="input-container">
                                    <input type="tel"
                                           name="secondary_phone"
                                           id="secondary_phone"
                                           value="{{ user.secondary_phone }}"
                                           class="form-input"
                                           placeholder="+254 700 000 000">
                                    <div class="input-icon">
                                        <i class="fas fa-phone-alt text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="secondary_phone_feedback"></div>
                            </div>

                            <!-- WhatsApp Number -->
                            <div class="form-field">
                                <label for="whatsapp_number" class="field-label">
                                    WhatsApp Number
                                </label>
                                <div class="input-container">
                                    <input type="tel"
                                           name="whatsapp_number"
                                           id="whatsapp_number"
                                           value="{{ user.whatsapp_number }}"
                                           class="form-input"
                                           placeholder="+254 700 000 000">
                                    <div class="input-icon">
                                        <i class="fab fa-whatsapp text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="whatsapp_number_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Customers can contact you directly via WhatsApp
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Address Information Section -->
                    <div class="form-section address-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                <i class="fas fa-map-marker-alt text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Address Information</h3>
                                <p class="section-subtitle">Your location and address details</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- City -->
                            <div class="form-field">
                                <label for="city" class="field-label">
                                    City
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="city"
                                           id="city"
                                           value="{{ user.city }}"
                                           class="form-input"
                                           placeholder="Enter your city">
                                    <div class="input-icon">
                                        <i class="fas fa-city text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="city_feedback"></div>
                            </div>

                            <!-- Country -->
                            <div class="form-field">
                                <label for="country" class="field-label">
                                    Country
                                </label>
                                <div class="select-container">
                                    <select name="country" id="country" class="form-select">
                                        <option value="Kenya" {% if user.country == 'Kenya' %}selected{% endif %}>Kenya</option>
                                        <option value="Uganda" {% if user.country == 'Uganda' %}selected{% endif %}>Uganda</option>
                                        <option value="Tanzania" {% if user.country == 'Tanzania' %}selected{% endif %}>Tanzania</option>
                                        <option value="Rwanda" {% if user.country == 'Rwanda' %}selected{% endif %}>Rwanda</option>
                                        <option value="Other" {% if user.country not in 'Kenya,Uganda,Tanzania,Rwanda' %}selected{% endif %}>Other</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="country_feedback"></div>
                            </div>

                            <!-- Full Address -->
                            <div class="form-field form-field-full">
                                <label for="address" class="field-label">
                                    Full Address
                                </label>
                                <div class="textarea-container">
                                    <textarea name="address"
                                              id="address"
                                              class="form-textarea"
                                              placeholder="Enter your complete address..."
                                              rows="3">{{ user.address }}</textarea>
                                </div>
                                <div class="field-feedback" id="address_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    This helps customers locate your business or for delivery purposes
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preferences Tab Content -->
            <div class="tab-content" id="preferences-tab" role="tabpanel" aria-labelledby="preferences-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Preferences & Settings</h2>
                    <p class="tab-description">Customize your account preferences and notification settings</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Language & Localization -->
                    <div class="form-section localization-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                                <i class="fas fa-globe text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Language & Region</h3>
                                <p class="section-subtitle">Set your preferred language and regional settings</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Preferred Language -->
                            <div class="form-field">
                                <label for="preferred_language" class="field-label">
                                    Preferred Language
                                </label>
                                <div class="select-container">
                                    <select name="preferred_language" id="preferred_language" class="form-select">
                                        <option value="en" {% if user.preferred_language == 'en' %}selected{% endif %}>English</option>
                                        <option value="sw" {% if user.preferred_language == 'sw' %}selected{% endif %}>Kiswahili</option>
                                        <option value="fr" {% if user.preferred_language == 'fr' %}selected{% endif %}>Français</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="preferred_language_feedback"></div>
                            </div>

                            <!-- Timezone -->
                            <div class="form-field">
                                <label for="timezone" class="field-label">
                                    Timezone
                                </label>
                                <div class="select-container">
                                    <select name="timezone" id="timezone" class="form-select">
                                        <option value="Africa/Nairobi" {% if user.timezone == 'Africa/Nairobi' %}selected{% endif %}>East Africa Time (EAT)</option>
                                        <option value="Africa/Lagos" {% if user.timezone == 'Africa/Lagos' %}selected{% endif %}>West Africa Time (WAT)</option>
                                        <option value="UTC" {% if user.timezone == 'UTC' %}selected{% endif %}>UTC</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="timezone_feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Preferences -->
                    <div class="form-section notifications-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-yellow-500 to-yellow-600">
                                <i class="fas fa-bell text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Notification Preferences</h3>
                                <p class="section-subtitle">Choose how you want to receive notifications</p>
                            </div>
                        </div>

                        <div class="notification-options">
                            <!-- Email Notifications -->
                            <div class="notification-option">
                                <div class="option-content">
                                    <div class="option-info">
                                        <h4 class="option-title">Email Notifications</h4>
                                        <p class="option-description">Receive important updates via email</p>
                                    </div>
                                    <div class="option-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="email_notifications" {% if user.email_notifications %}checked{% endif %}>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- SMS Notifications -->
                            <div class="notification-option">
                                <div class="option-content">
                                    <div class="option-info">
                                        <h4 class="option-title">SMS Notifications</h4>
                                        <p class="option-description">Get text messages for urgent updates</p>
                                    </div>
                                    <div class="option-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="sms_notifications" {% if user.sms_notifications %}checked{% endif %}>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Marketing Emails -->
                            <div class="notification-option">
                                <div class="option-content">
                                    <div class="option-info">
                                        <h4 class="option-title">Marketing Emails</h4>
                                        <p class="option-description">Receive promotional offers and updates</p>
                                    </div>
                                    <div class="option-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="marketing_emails" {% if user.marketing_emails %}checked{% endif %}>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Newsletter Subscription -->
                            <div class="notification-option">
                                <div class="option-content">
                                    <div class="option-info">
                                        <h4 class="option-title">Newsletter Subscription</h4>
                                        <p class="option-description">Stay updated with our monthly newsletter</p>
                                    </div>
                                    <div class="option-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="newsletter_subscription" {% if user.newsletter_subscription %}checked{% endif %}>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Privacy Settings -->
                    <div class="form-section privacy-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-purple-500 to-purple-600">
                                <i class="fas fa-user-shield text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Privacy Settings</h3>
                                <p class="section-subtitle">Control who can see your profile information</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Profile Visibility -->
                            <div class="form-field">
                                <label for="profile_visibility" class="field-label">
                                    Profile Visibility
                                </label>
                                <div class="select-container">
                                    <select name="profile_visibility" id="profile_visibility" class="form-select">
                                        <option value="public" {% if user.profile_visibility == 'public' %}selected{% endif %}>Public - Anyone can view</option>
                                        <option value="contacts_only" {% if user.profile_visibility == 'contacts_only' %}selected{% endif %}>Contacts Only</option>
                                        <option value="private" {% if user.profile_visibility == 'private' %}selected{% endif %}>Private - Only me</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="profile_visibility_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    This affects how others can find and view your profile
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab Content -->
            <div class="tab-content" id="security-tab" role="tabpanel" aria-labelledby="security-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Security & Privacy</h2>
                    <p class="tab-description">Manage your account security and password settings</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Password Management -->
                    <div class="form-section password-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-red-500 to-red-600">
                                <i class="fas fa-key text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Password Management</h3>
                                <p class="section-subtitle">Update your password and security settings</p>
                            </div>
                        </div>

                        <div class="password-change-form">
                            <div class="form-fields-grid">
                                <!-- Current Password -->
                                <div class="form-field">
                                    <label for="current_password" class="field-label">
                                        Current Password <span class="required">*</span>
                                    </label>
                                    <div class="input-container">
                                        <input type="password"
                                               name="current_password"
                                               id="current_password"
                                               class="form-input"
                                               placeholder="Enter your current password"
                                               required>
                                        <div class="input-icon">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye text-gray-400"></i>
                                        </button>
                                    </div>
                                    <div class="field-feedback" id="current_password_feedback"></div>
                                </div>

                                <!-- New Password -->
                                <div class="form-field">
                                    <label for="new_password" class="field-label">
                                        New Password <span class="required">*</span>
                                    </label>
                                    <div class="input-container">
                                        <input type="password"
                                               name="new_password"
                                               id="new_password"
                                               class="form-input"
                                               placeholder="Enter your new password"
                                               required>
                                        <div class="input-icon">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                            <i class="fas fa-eye text-gray-400"></i>
                                        </button>
                                    </div>
                                    <div class="field-feedback" id="new_password_feedback"></div>

                                    <!-- Password Strength Indicator -->
                                    <div class="password-strength">
                                        <div class="strength-bar">
                                            <div class="strength-fill" id="password_strength_fill"></div>
                                        </div>
                                        <div class="strength-text" id="password_strength_text">Password strength</div>
                                    </div>
                                </div>

                                <!-- Confirm New Password -->
                                <div class="form-field">
                                    <label for="confirm_password" class="field-label">
                                        Confirm New Password <span class="required">*</span>
                                    </label>
                                    <div class="input-container">
                                        <input type="password"
                                               name="confirm_password"
                                               id="confirm_password"
                                               class="form-input"
                                               placeholder="Confirm your new password"
                                               required>
                                        <div class="input-icon">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye text-gray-400"></i>
                                        </button>
                                    </div>
                                    <div class="field-feedback" id="confirm_password_feedback"></div>
                                </div>
                            </div>

                            <div class="password-requirements">
                                <h4 class="requirements-title">Password Requirements:</h4>
                                <ul class="requirements-list">
                                    <li class="requirement" id="req_length">
                                        <i class="fas fa-times text-red-500"></i>
                                        At least 8 characters long
                                    </li>
                                    <li class="requirement" id="req_uppercase">
                                        <i class="fas fa-times text-red-500"></i>
                                        Contains uppercase letter
                                    </li>
                                    <li class="requirement" id="req_lowercase">
                                        <i class="fas fa-times text-red-500"></i>
                                        Contains lowercase letter
                                    </li>
                                    <li class="requirement" id="req_number">
                                        <i class="fas fa-times text-red-500"></i>
                                        Contains number
                                    </li>
                                    <li class="requirement" id="req_special">
                                        <i class="fas fa-times text-red-500"></i>
                                        Contains special character
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Tab Content (Vendors Only) -->
            {% if user.role == 'vendor' %}
            <div class="tab-content" id="business-tab" role="tabpanel" aria-labelledby="business-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Business Information</h2>
                    <p class="tab-description">Manage your business details and company information</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Company Information -->
                    <div class="form-section company-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                <i class="fas fa-building text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Company Information</h3>
                                <p class="section-subtitle">Your business details and registration information</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Company Name -->
                            <div class="form-field">
                                <label for="company_name" class="field-label">
                                    Company Name <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="company_name"
                                           id="company_name"
                                           value="{{ vendor.company_name|default:'' }}"
                                           class="form-input"
                                           placeholder="Enter your company name"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-building text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="company_name_feedback"></div>
                            </div>

                            <!-- Business License -->
                            <div class="form-field">
                                <label for="business_license" class="field-label">
                                    Business License Number
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="business_license"
                                           id="business_license"
                                           value="{{ vendor.business_license|default:'' }}"
                                           class="form-input"
                                           placeholder="Enter your business license number">
                                    <div class="input-icon">
                                        <i class="fas fa-certificate text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="business_license_feedback"></div>
                            </div>

                            <!-- Business Type -->
                            <div class="form-field">
                                <label for="business_type" class="field-label">
                                    Business Type <span class="required">*</span>
                                </label>
                                <div class="select-container">
                                    <select name="business_type" id="business_type" class="form-select" required>
                                        <option value="">Select Business Type</option>
                                        <option value="dealership" {% if vendor.business_type == 'dealership' %}selected{% endif %}>Car Dealership</option>
                                        <option value="spare_parts" {% if vendor.business_type == 'spare_parts' %}selected{% endif %}>Spare Parts Seller</option>
                                        <option value="both" {% if vendor.business_type == 'both' %}selected{% endif %}>Both Cars and Spare Parts</option>
                                        <option value="service_center" {% if vendor.business_type == 'service_center' %}selected{% endif %}>Service Center</option>
                                        <option value="individual" {% if vendor.business_type == 'individual' %}selected{% endif %}>Individual Seller</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="business_type_feedback"></div>
                            </div>

                            <!-- Year Established -->
                            <div class="form-field">
                                <label for="year_established" class="field-label">
                                    Year Established
                                </label>
                                <div class="input-container">
                                    <input type="number"
                                           name="year_established"
                                           id="year_established"
                                           value="{{ vendor.year_established|default:'' }}"
                                           class="form-input"
                                           placeholder="e.g., 2010"
                                           min="1900"
                                           max="2025">
                                    <div class="input-icon">
                                        <i class="fas fa-calendar text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="year_established_feedback"></div>
                            </div>

                            <!-- Business Description -->
                            <div class="form-field form-field-full">
                                <label for="business_description" class="field-label">
                                    Business Description
                                </label>
                                <div class="textarea-container">
                                    <textarea name="business_description"
                                              id="business_description"
                                              class="form-textarea"
                                              placeholder="Describe your business, services, and specializations..."
                                              rows="4"
                                              maxlength="1000">{{ vendor.description|default:'' }}</textarea>
                                    <div class="textarea-counter">
                                        <span id="business_description_counter">{{ vendor.description|length|default:0 }}</span>/1000
                                    </div>
                                </div>
                                <div class="field-feedback" id="business_description_feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Business Contact Information -->
                    <div class="form-section business-contact-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-green-500 to-green-600">
                                <i class="fas fa-phone-office text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Business Contact</h3>
                                <p class="section-subtitle">How customers can reach your business</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Business Phone -->
                            <div class="form-field">
                                <label for="business_phone" class="field-label">
                                    Business Phone
                                </label>
                                <div class="input-container">
                                    <input type="tel"
                                           name="business_phone"
                                           id="business_phone"
                                           value="{{ vendor.business_phone|default:'' }}"
                                           class="form-input"
                                           placeholder="+254 700 000 000">
                                    <div class="input-icon">
                                        <i class="fas fa-phone text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="business_phone_feedback"></div>
                            </div>

                            <!-- Business Email -->
                            <div class="form-field">
                                <label for="business_email" class="field-label">
                                    Business Email
                                </label>
                                <div class="input-container">
                                    <input type="email"
                                           name="business_email"
                                           id="business_email"
                                           value="{{ vendor.business_email|default:'' }}"
                                           class="form-input"
                                           placeholder="<EMAIL>">
                                    <div class="input-icon">
                                        <i class="fas fa-envelope text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="business_email_feedback"></div>
                            </div>

                            <!-- Website -->
                            <div class="form-field">
                                <label for="website" class="field-label">
                                    Website
                                </label>
                                <div class="input-container">
                                    <input type="url"
                                           name="website"
                                           id="website"
                                           value="{{ vendor.website|default:'' }}"
                                           class="form-input"
                                           placeholder="https://www.yourcompany.com">
                                    <div class="input-icon">
                                        <i class="fas fa-globe text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="website_feedback"></div>
                            </div>

                            <!-- Physical Address -->
                            <div class="form-field form-field-full">
                                <label for="physical_address" class="field-label">
                                    Physical Address
                                </label>
                                <div class="textarea-container">
                                    <textarea name="physical_address"
                                              id="physical_address"
                                              class="form-textarea"
                                              placeholder="Enter your business physical address..."
                                              rows="3">{{ vendor.physical_address|default:'' }}</textarea>
                                </div>
                                <div class="field-feedback" id="physical_address_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    This helps customers locate your business for visits and deliveries
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Analytics Tab Content (Vendors and Admins) -->
            {% if user.role == 'vendor' or user.role == 'admin' %}
            <div class="tab-content" id="analytics-tab" role="tabpanel" aria-labelledby="analytics-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Analytics & Performance</h2>
                    <p class="tab-description">Track your performance metrics and business insights</p>
                </div>

                <div class="form-sections-grid">
                    {% if user.role == 'vendor' %}
                    <!-- Vendor Analytics -->
                    <div class="form-section analytics-overview-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-purple-500 to-purple-600">
                                <i class="fas fa-chart-line text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Business Overview</h3>
                                <p class="section-subtitle">Key performance indicators for your business</p>
                            </div>
                        </div>

                        <div class="analytics-grid">
                            <!-- Profile Views -->
                            <div class="analytics-card">
                                <div class="analytics-card-header">
                                    <div class="analytics-icon bg-blue-500">
                                        <i class="fas fa-eye text-white"></i>
                                    </div>
                                    <div class="analytics-info">
                                        <h4 class="analytics-title">Profile Views</h4>
                                        <p class="analytics-subtitle">This month</p>
                                    </div>
                                </div>
                                <div class="analytics-value">
                                    <span class="analytics-number">{{ vendor.profile_views|default:0 }}</span>
                                    <span class="analytics-change positive">+12%</span>
                                </div>
                                <div class="analytics-chart">
                                    <div class="mini-chart">
                                        <div class="chart-bar" style="height: 20%"></div>
                                        <div class="chart-bar" style="height: 40%"></div>
                                        <div class="chart-bar" style="height: 60%"></div>
                                        <div class="chart-bar" style="height: 80%"></div>
                                        <div class="chart-bar" style="height: 100%"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Inquiries -->
                            <div class="analytics-card">
                                <div class="analytics-card-header">
                                    <div class="analytics-icon bg-green-500">
                                        <i class="fas fa-comments text-white"></i>
                                    </div>
                                    <div class="analytics-info">
                                        <h4 class="analytics-title">Inquiries</h4>
                                        <p class="analytics-subtitle">Total received</p>
                                    </div>
                                </div>
                                <div class="analytics-value">
                                    <span class="analytics-number">{{ vendor.total_inquiries|default:0 }}</span>
                                    <span class="analytics-change positive">+8%</span>
                                </div>
                                <div class="analytics-chart">
                                    <div class="mini-chart">
                                        <div class="chart-bar" style="height: 30%"></div>
                                        <div class="chart-bar" style="height: 50%"></div>
                                        <div class="chart-bar" style="height: 70%"></div>
                                        <div class="chart-bar" style="height: 90%"></div>
                                        <div class="chart-bar" style="height: 100%"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Average Rating -->
                            <div class="analytics-card">
                                <div class="analytics-card-header">
                                    <div class="analytics-icon bg-yellow-500">
                                        <i class="fas fa-star text-white"></i>
                                    </div>
                                    <div class="analytics-info">
                                        <h4 class="analytics-title">Rating</h4>
                                        <p class="analytics-subtitle">Average score</p>
                                    </div>
                                </div>
                                <div class="analytics-value">
                                    <span class="analytics-number">{{ vendor.average_rating|default:0|floatformat:1 }}</span>
                                    <span class="analytics-change neutral">0%</span>
                                </div>
                                <div class="analytics-stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= vendor.average_rating|default:0 %}
                                            <i class="fas fa-star text-yellow-400"></i>
                                        {% else %}
                                            <i class="far fa-star text-gray-300"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>

                            <!-- Response Time -->
                            <div class="analytics-card">
                                <div class="analytics-card-header">
                                    <div class="analytics-icon bg-red-500">
                                        <i class="fas fa-clock text-white"></i>
                                    </div>
                                    <div class="analytics-info">
                                        <h4 class="analytics-title">Response Time</h4>
                                        <p class="analytics-subtitle">Average hours</p>
                                    </div>
                                </div>
                                <div class="analytics-value">
                                    <span class="analytics-number">{{ vendor.response_time_hours|default:24 }}h</span>
                                    <span class="analytics-change negative">-5%</span>
                                </div>
                                <div class="analytics-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 75%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Business Performance -->
                    <div class="form-section performance-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                                <i class="fas fa-trophy text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Performance Metrics</h3>
                                <p class="section-subtitle">Detailed business performance analysis</p>
                            </div>
                        </div>

                        <div class="performance-grid">
                            <!-- Profile Completion -->
                            <div class="performance-item">
                                <div class="performance-header">
                                    <h4 class="performance-title">Profile Completion</h4>
                                    <span class="performance-percentage">85%</span>
                                </div>
                                <div class="performance-bar">
                                    <div class="performance-fill" style="width: 85%"></div>
                                </div>
                                <p class="performance-description">Complete your profile to attract more customers</p>
                            </div>

                            <!-- Customer Satisfaction -->
                            <div class="performance-item">
                                <div class="performance-header">
                                    <h4 class="performance-title">Customer Satisfaction</h4>
                                    <span class="performance-percentage">92%</span>
                                </div>
                                <div class="performance-bar">
                                    <div class="performance-fill" style="width: 92%"></div>
                                </div>
                                <p class="performance-description">Based on customer reviews and ratings</p>
                            </div>

                            <!-- Market Presence -->
                            <div class="performance-item">
                                <div class="performance-header">
                                    <h4 class="performance-title">Market Presence</h4>
                                    <span class="performance-percentage">78%</span>
                                </div>
                                <div class="performance-bar">
                                    <div class="performance-fill" style="width: 78%"></div>
                                </div>
                                <p class="performance-description">Your visibility in the marketplace</p>
                            </div>
                        </div>
                    </div>

                    {% elif user.role == 'admin' %}
                    <!-- Admin Analytics -->
                    <div class="form-section admin-analytics-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-red-500 to-red-600">
                                <i class="fas fa-chart-bar text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">System Overview</h3>
                                <p class="section-subtitle">Platform-wide analytics and metrics</p>
                            </div>
                        </div>

                        <div class="analytics-grid">
                            <!-- Total Users -->
                            <div class="analytics-card">
                                <div class="analytics-card-header">
                                    <div class="analytics-icon bg-blue-500">
                                        <i class="fas fa-users text-white"></i>
                                    </div>
                                    <div class="analytics-info">
                                        <h4 class="analytics-title">Total Users</h4>
                                        <p class="analytics-subtitle">All registered</p>
                                    </div>
                                </div>
                                <div class="analytics-value">
                                    <span class="analytics-number">{{ total_users|default:0 }}</span>
                                    <span class="analytics-change positive">+15%</span>
                                </div>
                            </div>

                            <!-- Active Vendors -->
                            <div class="analytics-card">
                                <div class="analytics-card-header">
                                    <div class="analytics-icon bg-green-500">
                                        <i class="fas fa-store text-white"></i>
                                    </div>
                                    <div class="analytics-info">
                                        <h4 class="analytics-title">Active Vendors</h4>
                                        <p class="analytics-subtitle">Verified sellers</p>
                                    </div>
                                </div>
                                <div class="analytics-value">
                                    <span class="analytics-number">{{ active_vendors|default:0 }}</span>
                                    <span class="analytics-change positive">+8%</span>
                                </div>
                            </div>

                            <!-- Total Listings -->
                            <div class="analytics-card">
                                <div class="analytics-card-header">
                                    <div class="analytics-icon bg-purple-500">
                                        <i class="fas fa-car text-white"></i>
                                    </div>
                                    <div class="analytics-info">
                                        <h4 class="analytics-title">Car Listings</h4>
                                        <p class="analytics-subtitle">Active listings</p>
                                    </div>
                                </div>
                                <div class="analytics-value">
                                    <span class="analytics-number">{{ total_listings|default:0 }}</span>
                                    <span class="analytics-change positive">+12%</span>
                                </div>
                            </div>

                            <!-- System Health -->
                            <div class="analytics-card">
                                <div class="analytics-card-header">
                                    <div class="analytics-icon bg-orange-500">
                                        <i class="fas fa-heartbeat text-white"></i>
                                    </div>
                                    <div class="analytics-info">
                                        <h4 class="analytics-title">System Health</h4>
                                        <p class="analytics-subtitle">Overall status</p>
                                    </div>
                                </div>
                                <div class="analytics-value">
                                    <span class="analytics-number">98%</span>
                                    <span class="analytics-change positive">+2%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Enhanced Form Submit Actions -->
            <div class="form-submit-actions flex flex-col sm:flex-row gap-4 p-6 bg-gray-50/80 backdrop-blur-sm border-t border-gray-200/50">
                <button type="button" class="submit-btn submit-btn-secondary flex-1 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-semibold font-montserrat transition-all duration-300 flex items-center justify-center" onclick="resetForm()">
                    <i class="fas fa-undo mr-2"></i>
                    Reset Changes
                </button>
                <button type="submit" class="submit-btn submit-btn-primary flex-1 px-6 py-3 bg-gradient-to-r from-harrier-red to-harrier-red-dark hover:from-harrier-red-dark hover:to-harrier-red text-white rounded-xl font-semibold font-montserrat transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center" id="saveProfileBtn">
                    <i class="fas fa-save mr-2"></i>
                    <span class="save-btn-text">Save Profile</span>
                    <div class="save-btn-spinner hidden ml-2">
                        <div class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    </div>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Toast Notification Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

<!-- Debug Test Buttons (remove in production) -->
<div class="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
    <button onclick="simpleShowToast('Toast system working!', 'success')" class="bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-green-600 transition-colors cursor-pointer" style="font-size: 12px;">
        Test Toast
    </button>
    <button onclick="handleStatClick('complete')" class="bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-600 transition-colors cursor-pointer" style="font-size: 12px;">
        Test Stat Click
    </button>
    <button onclick="handleContactClick('email', '<EMAIL>')" class="bg-purple-500 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-purple-600 transition-colors cursor-pointer" style="font-size: 12px;">
        Test Contact
    </button>
</div>

<!-- Enhanced JavaScript for Profile Management -->
<script>
// Global variables
window.showToast = null;
window.handleStatClick = null;
window.handleContactClick = null;
window.handleBadgeClick = null;

// Simple toast system
function simpleShowToast(message, type) {
    console.log('Toast:', message, type);

    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(container);
    }

    const toast = document.createElement('div');
    let bgColor = 'bg-blue-500';
    if (type === 'success') bgColor = 'bg-green-500';
    if (type === 'error') bgColor = 'bg-red-500';
    if (type === 'warning') bgColor = 'bg-yellow-500';

    toast.className = `${bgColor} text-white p-4 rounded-lg shadow-lg mb-2`;
    toast.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200 text-xl">&times;</button>
        </div>
    `;

    container.appendChild(toast);
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}

// Click handler functions
function handleStatClick(type) {
    console.log('Stat clicked:', type);
    simpleShowToast(`Clicked on ${type} stat!`, 'success');

    switch(type) {
        case 'complete':
            simpleShowToast('Showing incomplete profile fields...', 'info');
            break;
        case 'listings':
            simpleShowToast('Redirecting to listings...', 'info');
            setTimeout(() => {
                try {
                    window.location.href = '/dashboard/listings/';
                } catch(e) {
                    simpleShowToast('Navigation error', 'error');
                }
            }, 1500);
            break;
        case 'orders':
            simpleShowToast('Redirecting to orders...', 'info');
            setTimeout(() => {
                try {
                    window.location.href = '/dashboard/orders/';
                } catch(e) {
                    simpleShowToast('Navigation error', 'error');
                }
            }, 1500);
            break;
        case 'member since':
            simpleShowToast('You joined Gurumisha Motors!', 'info');
            break;
        default:
            simpleShowToast('Feature coming soon!', 'info');
    }
}

function handleContactClick(type, value) {
    console.log('Contact clicked:', type, value);

    try {
        switch(type) {
            case 'email':
                window.location.href = `mailto:${value}`;
                simpleShowToast('Opening email client...', 'info');
                break;
            case 'phone':
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(value).then(() => {
                        simpleShowToast('Phone number copied!', 'success');
                    }).catch(() => {
                        simpleShowToast(`Phone: ${value}`, 'info');
                    });
                } else {
                    simpleShowToast(`Phone: ${value}`, 'info');
                }
                break;
            case 'location':
                window.open(`https://maps.google.com/?q=${encodeURIComponent(value)}`, '_blank');
                simpleShowToast('Opening maps...', 'info');
                break;
            default:
                simpleShowToast('Contact action not supported', 'warning');
        }
    } catch(e) {
        console.error('Contact click error:', e);
        simpleShowToast('Error handling contact action', 'error');
    }
}

function handleBadgeClick(text) {
    console.log('Badge clicked:', text);
    simpleShowToast(`Badge: ${text}`, 'info');
}

// Test function
function testClickFunctionality() {
    console.log('Testing click functionality...');
    simpleShowToast('Click test successful!', 'success');
}

// Make functions globally available immediately
window.showToast = simpleShowToast;
window.handleStatClick = handleStatClick;
window.handleContactClick = handleContactClick;
window.handleBadgeClick = handleBadgeClick;
window.testClickFunctionality = testClickFunctionality;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - setting up click handlers');

    // Ensure functions are available
    window.showToast = simpleShowToast;
    window.handleStatClick = handleStatClick;
    window.handleContactClick = handleContactClick;

    // Simple setup with timeout to ensure DOM is ready
    setTimeout(() => {
        console.log('Setting up click handlers...');

        // Add cursor pointer to clickable elements
        const clickableElements = document.querySelectorAll('.stat-card, .contact-item, .status-badge, .role-badge');
        clickableElements.forEach(el => {
            el.style.cursor = 'pointer';
        });

        console.log('Click handlers ready. Elements should work with onclick attributes.');
    }, 500);

    // Initialize basic features only
    try {
        // Basic tab navigation
        const tabs = document.querySelectorAll('.nav-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');
                if (targetTab) {
                    switchToTab(targetTab);
                }
            });
        });

        console.log('Basic features initialized');
    } catch (e) {
        console.log('Feature initialization error:', e);
    }
});

// Simple tab switching function
function switchToTab(tabName) {
    console.log('Switching to tab:', tabName);

    // Update tab buttons
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
    });

    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
        activeTab.setAttribute('aria-selected', 'true');
    }

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    const targetContent = document.getElementById(`${tabName}-tab`);
    if (targetContent) {
        targetContent.classList.add('active');
    }

    // Update mobile selector
    const mobileSelect = document.querySelector('.mobile-tab-select');
    if (mobileSelect) {
        mobileSelect.value = tabName;
    }
}

console.log('Profile JavaScript loaded successfully');

    // Character counter functionality for textareas
    const textareas = document.querySelectorAll('textarea[maxlength]');
    textareas.forEach(textarea => {
        const counter = document.getElementById(textarea.id + '_counter');
        if (counter) {
            textarea.addEventListener('input', function() {
                counter.textContent = this.value.length;

                // Update color based on length
                const maxLength = parseInt(this.getAttribute('maxlength'));
                const percentage = (this.value.length / maxLength) * 100;

                if (percentage > 90) {
                    counter.style.color = '#ef4444';
                } else if (percentage > 75) {
                    counter.style.color = '#f59e0b';
                } else {
                    counter.style.color = '#6b7280';
                }
            });
        }
    });

    // Real-time field validation
    const inputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

// Tab Navigation
function initializeTabNavigation() {
    const tabs = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            switchToTab(targetTab);
        });
    });
}

function switchToTab(tabName) {
    // Update tab buttons with enhanced animations
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');

        // Reset tab styling
        const tabIcon = tab.querySelector('.tab-icon');
        const tabTitle = tab.querySelector('.tab-title');
        const tabIndicator = tab.querySelector('.tab-indicator');

        if (tabIcon) {
            tabIcon.style.background = '#f3f4f6';
            const icon = tabIcon.querySelector('i');
            if (icon) icon.style.color = '#6b7280';
        }
        if (tabTitle) tabTitle.style.color = '#374151';
        if (tabIndicator) {
            tabIndicator.style.background = 'transparent';
            tabIndicator.style.height = '1px';
        }
    });

    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
        activeTab.setAttribute('aria-selected', 'true');

        // Apply active styling
        const tabIcon = activeTab.querySelector('.tab-icon');
        const tabTitle = activeTab.querySelector('.tab-title');
        const tabIndicator = activeTab.querySelector('.tab-indicator');

        if (tabIcon) {
            tabIcon.style.background = 'var(--harrier-red)';
            const icon = tabIcon.querySelector('i');
            if (icon) icon.style.color = 'white';
        }
        if (tabTitle) tabTitle.style.color = 'var(--harrier-red)';
        if (tabIndicator) {
            tabIndicator.style.background = 'var(--harrier-red)';
            tabIndicator.style.height = '3px';
        }

        // Scroll tab into view if needed (for mobile horizontal scroll)
        activeTab.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
        });
    }

    // Update tab content with smooth transition
    const currentActive = document.querySelector('.tab-content.active');
    const targetContent = document.getElementById(`${tabName}-tab`);

    if (currentActive && targetContent && currentActive !== targetContent) {
        // Fade out current content
        currentActive.style.opacity = '0';
        currentActive.style.transform = 'translateY(20px)';

        setTimeout(() => {
            currentActive.classList.remove('active');

            // Fade in new content
            targetContent.classList.add('active');
            targetContent.style.opacity = '0';
            targetContent.style.transform = 'translateY(20px)';

            setTimeout(() => {
                targetContent.style.opacity = '1';
                targetContent.style.transform = 'translateY(0)';
            }, 100);
        }, 200);
    } else if (targetContent) {
        // Remove active from all first
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Add active to target
        targetContent.classList.add('active');
        targetContent.style.opacity = '1';
        targetContent.style.transform = 'translateY(0)';
    }

    // Update mobile selector
    const mobileSelect = document.querySelector('.mobile-tab-select');
    if (mobileSelect) {
        mobileSelect.value = tabName;
    }

    // Update URL hash without triggering scroll
    if (history.pushState) {
        history.pushState(null, null, `#${tabName}`);
    } else {
        window.location.hash = tabName;
    }

    // Scroll to top of form container
    const formContainer = document.querySelector('.form-container');
    if (formContainer) {
        formContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Announce tab change to screen readers
    if (window.announceToScreenReader) {
        const tabTitle = activeTab ? activeTab.querySelector('.tab-title').textContent : tabName;
        window.announceToScreenReader(`Switched to ${tabTitle} section`);
    }

    // Focus management for accessibility
    setTimeout(() => {
        if (targetContent) {
            const firstInput = targetContent.querySelector('input:not([type="hidden"]), select, textarea, button:not([disabled])');
            if (firstInput) {
                firstInput.focus();
            }
        }
    }, 400);
}

// Scroll to specific section
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        // First switch to the tab
        const tabName = sectionId.replace('-tab', '');
        switchToTab(tabName);

        // Then scroll to the section
        setTimeout(() => {
            section.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }, 300);
    }
}

// Password Toggle
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = field.parentElement.querySelector('.password-toggle i');

    if (field.type === 'password') {
        field.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Password Strength Checker
function initializePasswordStrength() {
    const passwordField = document.getElementById('new_password');
    if (!passwordField) return;

    passwordField.addEventListener('input', function() {
        checkPasswordStrength(this.value);
    });
}

function checkPasswordStrength(password) {
    const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    // Update requirement indicators
    Object.keys(requirements).forEach(req => {
        const element = document.getElementById(`req_${req}`);
        if (element) {
            const icon = element.querySelector('i');
            if (requirements[req]) {
                element.classList.add('met');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-check');
            } else {
                element.classList.remove('met');
                icon.classList.remove('fa-check');
                icon.classList.add('fa-times');
            }
        }
    });

    // Calculate strength
    const metRequirements = Object.values(requirements).filter(Boolean).length;
    const strengthFill = document.getElementById('password_strength_fill');
    const strengthText = document.getElementById('password_strength_text');

    if (strengthFill && strengthText) {
        strengthFill.className = 'strength-fill';

        if (metRequirements === 0) {
            strengthText.textContent = 'Password strength';
        } else if (metRequirements <= 2) {
            strengthFill.classList.add('weak');
            strengthText.textContent = 'Weak password';
        } else if (metRequirements <= 3) {
            strengthFill.classList.add('fair');
            strengthText.textContent = 'Fair password';
        } else if (metRequirements <= 4) {
            strengthFill.classList.add('good');
            strengthText.textContent = 'Good password';
        } else {
            strengthFill.classList.add('strong');
            strengthText.textContent = 'Strong password';
        }
    }
}

// Image Upload Preview
function initializeImageUpload() {
    const profileInput = document.getElementById('profilePictureInput');
    if (profileInput) {
        profileInput.addEventListener('change', function() {
            previewProfileImage(this);
        });
    }

    // Add drag and drop functionality
    const avatarSection = document.querySelector('.profile-avatar-section');
    if (avatarSection) {
        avatarSection.addEventListener('dragover', handleDragOver);
        avatarSection.addEventListener('drop', handleDrop);
        avatarSection.addEventListener('dragleave', handleDragLeave);
    }
}

function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.add('drag-over');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('drag-over');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            const input = document.getElementById('profilePictureInput');
            const dt = new DataTransfer();
            dt.items.add(file);
            input.files = dt.files;
            previewProfileImage(input);
        } else {
            showToast('Please select a valid image file', 'error');
        }
    }
}

function previewProfileImage(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            showToast('Image size must be less than 5MB', 'error');
            input.value = '';
            return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            showToast('Please select a valid image file', 'error');
            input.value = '';
            return;
        }

        // Show loading state
        showImageUploadProgress();

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                updateProfileImagePreview(e.target.result);
                showToast('Image uploaded successfully!', 'success');
                hideImageUploadProgress();
            } catch (error) {
                showToast('Error processing image', 'error');
                hideImageUploadProgress();
                input.value = '';
            }
        };

        reader.onerror = function() {
            showToast('Error reading image file', 'error');
            hideImageUploadProgress();
            input.value = '';
        };

        reader.readAsDataURL(file);
    }
}

function updateProfileImagePreview(imageSrc) {
    // Update hero section profile image
    const heroPreview = document.getElementById('profilePreview');
    if (heroPreview) {
        if (heroPreview.tagName === 'IMG') {
            heroPreview.src = imageSrc;
            heroPreview.classList.add('animate-fade-in');
        } else {
            // Replace placeholder with image
            const img = document.createElement('img');
            img.src = imageSrc;
            img.className = 'profile-avatar w-full h-full object-cover rounded-full shadow-2xl border-4 border-white/40 transition-all duration-300 animate-fade-in';
            img.id = 'profilePreview';
            img.alt = 'Profile Picture';
            heroPreview.parentNode.replaceChild(img, heroPreview);
        }
    }

    // Update form section profile image if it exists
    const formPreview = document.getElementById('profileImagePreview');
    if (formPreview) {
        if (formPreview.tagName === 'IMG') {
            formPreview.src = imageSrc;
        } else {
            const img = document.createElement('img');
            img.src = imageSrc;
            img.className = 'profile-image w-full h-full object-cover rounded-full';
            img.id = 'profileImagePreview';
            img.alt = 'Profile Picture';
            formPreview.parentNode.replaceChild(img, formPreview);
        }
    }
}

function removeProfilePicture() {
    const input = document.getElementById('profilePictureInput');
    const heroPreview = document.getElementById('profilePreview');
    const formPreview = document.getElementById('profileImagePreview');

    if (input) {
        // Reset input
        input.value = '';

        // Get user's first name or username for placeholder
        const userName = '{{ user.first_name|first|default:user.username|first|upper }}';

        // Replace hero image with placeholder
        if (heroPreview) {
            if (heroPreview.tagName === 'IMG') {
                const placeholder = document.createElement('div');
                placeholder.className = 'profile-avatar w-full h-full bg-white/25 backdrop-blur-sm rounded-full shadow-2xl border-4 border-white/40 flex items-center justify-center text-white font-bold text-4xl lg:text-5xl font-montserrat transition-all duration-300';
                placeholder.id = 'profilePreview';
                placeholder.textContent = userName;
                heroPreview.parentNode.replaceChild(placeholder, heroPreview);
            }
        }

        // Replace form image with placeholder if it exists
        if (formPreview && formPreview.tagName === 'IMG') {
            const placeholder = document.createElement('div');
            placeholder.className = 'profile-placeholder w-full h-full bg-gray-200 rounded-full flex items-center justify-center';
            placeholder.id = 'profileImagePreview';
            placeholder.innerHTML = '<i class="fas fa-user text-4xl text-gray-400"></i>';
            formPreview.parentNode.replaceChild(placeholder, formPreview);
        }

        showToast('Profile picture removed', 'info');
    }
}

// Form Validation
function initializeFormValidation() {
    const form = document.getElementById('profileForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
        });
    }
}

function validateForm() {
    let isValid = true;
    const requiredFields = document.querySelectorAll('[required]');

    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    // Validate password confirmation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');

    if (newPassword && confirmPassword && newPassword.value) {
        if (newPassword.value !== confirmPassword.value) {
            showFieldError(confirmPassword, 'Passwords do not match');
            isValid = false;
        }
    }

    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldName = field.name;

    // Clear previous errors
    clearFieldError(field);

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'This field is required');
        return false;
    }

    // Email validation
    if (fieldType === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'Please enter a valid email address');
            return false;
        }
    }

    // Phone validation
    if (fieldName.includes('phone') && value) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            showFieldError(field, 'Please enter a valid phone number');
            return false;
        }
    }

    return true;
}

function showFieldError(field, message) {
    const feedback = document.getElementById(field.id + '_feedback');
    if (feedback) {
        feedback.textContent = message;
        feedback.className = 'field-feedback error';
    }

    field.style.borderColor = '#ef4444';
}

function clearFieldError(field) {
    const feedback = document.getElementById(field.id + '_feedback');
    if (feedback) {
        feedback.textContent = '';
        feedback.className = 'field-feedback';
    }

    field.style.borderColor = '';
}

// Auto-save functionality
function initializeAutoSave() {
    let autoSaveTimeout;
    const form = document.getElementById('profileForm');

    if (form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    // Auto-save logic here
                    console.log('Auto-saving profile...');
                }, 2000);
            });
        });
    }
}

// Notification toggles
function initializeNotificationToggles() {
    const toggles = document.querySelectorAll('.toggle-switch input');
    toggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            // Add visual feedback
            const option = this.closest('.notification-option');
            if (option) {
                option.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    option.style.transform = '';
                }, 150);
            }
        });
    });
}

// Profile completion calculation
function calculateProfileCompletion() {
    const fields = [
        'first_name', 'last_name', 'email', 'phone', 'bio',
        'city', 'address'
    ];

    let completed = 0;
    let total = fields.length;

    // Check profile picture
    const profilePicture = document.getElementById('profileImagePreview');
    if (profilePicture && profilePicture.tagName === 'IMG') {
        completed++;
    }
    total++;

    fields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && field.value && field.value.trim()) {
            completed++;
        }
    });

    const percentage = Math.round((completed / total) * 100);

    // Update progress indicators
    updateProgressIndicators(percentage);

    return percentage;
}

function updateProgressIndicators(percentage) {
    // Update hero section progress ring
    const heroRing = document.querySelector('.profile-avatar-section svg circle:last-child');
    if (heroRing) {
        const circumference = 2 * Math.PI * 45;
        const offset = circumference - (percentage / 100) * circumference;
        heroRing.style.strokeDashoffset = offset;
    }

    // Update completion banner
    const completionRing = document.querySelector('.progress-ring-fill');
    if (completionRing) {
        const circumference = 2 * Math.PI * 26;
        const offset = circumference - (percentage / 100) * circumference;
        completionRing.style.strokeDashoffset = offset;
    }

    // Update percentage text
    const percentageTexts = document.querySelectorAll('.progress-text span, .profile-avatar-section span');
    percentageTexts.forEach(text => {
        if (text.textContent.includes('%')) {
            text.textContent = percentage + '%';
        }
    });
}

// Form reset
function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This action cannot be undone.')) {
        document.getElementById('profileForm').reset();

        // Reset profile picture preview
        const preview = document.getElementById('profileImagePreview');
        if (preview && preview.tagName === 'IMG') {
            // Restore original image or placeholder
            location.reload();
        }

        // Clear all feedback messages
        document.querySelectorAll('.field-feedback').forEach(feedback => {
            feedback.textContent = '';
            feedback.className = 'field-feedback';
        });

        // Reset password strength
        const strengthFill = document.getElementById('password_strength_fill');
        const strengthText = document.getElementById('password_strength_text');
        if (strengthFill && strengthText) {
            strengthFill.className = 'strength-fill';
            strengthText.textContent = 'Password strength';
        }

        // Reset requirements
        document.querySelectorAll('.requirement').forEach(req => {
            req.classList.remove('met');
            const icon = req.querySelector('i');
            icon.classList.remove('fa-check');
            icon.classList.add('fa-times');
        });
    }
}

// Enhanced save profile functionality
function saveProfile() {
    const form = document.getElementById('profileForm');
    const saveBtn = document.getElementById('saveProfileBtn');
    const saveBtnText = saveBtn.querySelector('.save-btn-text');

    if (!form) {
        console.error('Profile form not found');
        return;
    }

    // Validate form before submission
    if (!validateForm()) {
        showToast('Please correct the errors before saving', 'error');
        return;
    }

    // Update button state
    saveBtn.disabled = true;
    if (saveBtnText) {
        saveBtnText.textContent = 'Saving...';
    }
    saveBtn.querySelector('i').className = 'fas fa-spinner fa-spin mr-2';

    // Submit form
    form.submit();
}

// Auto-save functionality
let autoSaveTimeout;
let hasUnsavedChanges = false;

function enableAutoSave() {
    const form = document.getElementById('profileForm');
    if (!form) return;

    // Track changes on form inputs
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            hasUnsavedChanges = true;
            clearTimeout(autoSaveTimeout);

            // Auto-save after 2 seconds of inactivity
            autoSaveTimeout = setTimeout(() => {
                autoSaveField(this);
            }, 2000);
        });

        input.addEventListener('blur', function() {
            // Auto-save immediately on blur for important fields
            if (['first_name', 'last_name', 'email', 'phone'].includes(this.name)) {
                clearTimeout(autoSaveTimeout);
                autoSaveField(this);
            }
        });
    });
}

function autoSaveField(field) {
    if (!field.name || field.type === 'file') return;

    const formData = new FormData();
    formData.append('field_name', field.name);
    formData.append('field_value', field.value);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Auto-Save': 'true',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAutoSaveIndicator();
            hasUnsavedChanges = false;
        } else {
            console.error('Auto-save failed:', data.message);
        }
    })
    .catch(error => {
        console.error('Auto-save error:', error);
    });
}

function showAutoSaveIndicator() {
    const indicator = document.getElementById('autoSaveIndicator');
    if (indicator) {
        indicator.style.opacity = '1';
        indicator.style.transform = 'translateY(0)';

        setTimeout(() => {
            indicator.style.opacity = '0';
            indicator.style.transform = 'translateY(-10px)';
        }, 3000);
    }
}

// Enhanced real-time validation
function enableRealTimeValidation() {
    const form = document.getElementById('profileForm');
    if (!form) return;

    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        // Validate on blur (when user leaves field)
        input.addEventListener('blur', function() {
            validateField(this);
        });

        // Clear validation state on input (while typing)
        input.addEventListener('input', function() {
            // For certain fields, validate while typing (with debounce)
            if (this.name === 'username' || this.name === 'email') {
                clearTimeout(this.validationTimeout);
                this.validationTimeout = setTimeout(() => {
                    validateField(this);
                }, 500);
            } else {
                // Just clear validation state for other fields
                clearFieldValidation(this);
            }

            // Update character counters
            updateCharacterCounter(this);
        });

        // Validate on change for select elements
        input.addEventListener('change', function() {
            validateField(this);
        });
    });
}

// Character counter for textareas and inputs with maxlength
function updateCharacterCounter(field) {
    const maxLength = field.getAttribute('maxlength');
    if (!maxLength) return;

    const counter = document.getElementById(field.id + '_counter');
    if (!counter) return;

    const currentLength = field.value.length;
    const remaining = maxLength - currentLength;

    counter.textContent = currentLength;

    // Update counter color based on usage
    const percentage = (currentLength / maxLength) * 100;
    if (percentage > 90) {
        counter.style.color = '#ef4444';
    } else if (percentage > 75) {
        counter.style.color = '#f59e0b';
    } else {
        counter.style.color = '#6b7280';
    }
}

// Form-wide validation
function validateForm() {
    const form = document.getElementById('profileForm');
    if (!form) return false;

    let isValid = true;
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');

    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });

    // Show summary message
    if (!isValid) {
        showToast('Please correct the errors in the form before saving', 'error');
    }

    return isValid;
}

function validateField(field) {
    const fieldContainer = field.closest('.form-field');
    const feedback = fieldContainer.querySelector('.field-feedback');

    // Clear previous validation state
    clearFieldValidation(field);

    // Required field validation
    if (!field.value.trim() && field.required) {
        showFieldError(field, 'This field is required');
        return false;
    }

    // Skip validation if field is empty and not required
    if (!field.value.trim() && !field.required) {
        return true;
    }

    // Email validation
    if (field.type === 'email' && field.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            return false;
        }

        // Check for common email providers
        const commonProviders = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
        const domain = field.value.split('@')[1];
        if (domain && !commonProviders.includes(domain.toLowerCase())) {
            showFieldWarning(field, 'Please double-check your email domain');
        } else {
            showFieldSuccess(field, 'Valid email address');
        }
        return true;
    }

    // Phone validation
    if ((field.name === 'phone' || field.name === 'secondary_phone' || field.name === 'whatsapp_number') && field.value) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(field.value)) {
            showFieldError(field, 'Please enter a valid phone number');
            return false;
        }

        // Kenya phone number specific validation
        if (field.value.startsWith('+254') || field.value.startsWith('254') || field.value.startsWith('07') || field.value.startsWith('01')) {
            showFieldSuccess(field, 'Valid Kenyan phone number');
        } else {
            showFieldSuccess(field, 'Valid phone number');
        }
        return true;
    }

    // Username validation
    if (field.name === 'username' && field.value) {
        if (field.value.length < 3) {
            showFieldError(field, 'Username must be at least 3 characters long');
            return false;
        }

        if (field.value.length > 30) {
            showFieldError(field, 'Username must be less than 30 characters');
            return false;
        }

        const usernameRegex = /^[a-zA-Z0-9_-]+$/;
        if (!usernameRegex.test(field.value)) {
            showFieldError(field, 'Username can only contain letters, numbers, hyphens, and underscores');
            return false;
        }

        showFieldSuccess(field, 'Username looks good');
        return true;
    }

    // Name validation
    if ((field.name === 'first_name' || field.name === 'last_name') && field.value) {
        if (field.value.length < 2) {
            showFieldError(field, 'Name must be at least 2 characters long');
            return false;
        }

        const nameRegex = /^[a-zA-Z\s'-]+$/;
        if (!nameRegex.test(field.value)) {
            showFieldError(field, 'Name can only contain letters, spaces, hyphens, and apostrophes');
            return false;
        }

        showFieldSuccess(field, 'Valid name');
        return true;
    }

    // Bio validation
    if (field.name === 'bio' && field.value) {
        const wordCount = field.value.trim().split(/\s+/).length;
        if (wordCount < 5) {
            showFieldWarning(field, 'Consider adding more details to your bio');
        } else if (wordCount > 100) {
            showFieldWarning(field, 'Consider keeping your bio concise');
        } else {
            showFieldSuccess(field, 'Good bio length');
        }
        return true;
    }

    // Date of birth validation
    if (field.name === 'date_of_birth' && field.value) {
        const birthDate = new Date(field.value);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();

        if (age < 13) {
            showFieldError(field, 'You must be at least 13 years old');
            return false;
        }

        if (age > 120) {
            showFieldError(field, 'Please enter a valid birth date');
            return false;
        }

        showFieldSuccess(field, 'Valid birth date');
        return true;
    }

    // City validation
    if (field.name === 'city' && field.value) {
        const cityRegex = /^[a-zA-Z\s'-]+$/;
        if (!cityRegex.test(field.value)) {
            showFieldError(field, 'City name can only contain letters, spaces, hyphens, and apostrophes');
            return false;
        }

        showFieldSuccess(field, 'Valid city name');
        return true;
    }

    // Default success for other fields
    if (field.value.trim()) {
        showFieldSuccess(field);
    }

    return true;
}

function showFieldError(field, message) {
    const fieldContainer = field.closest('.form-field');
    const feedback = fieldContainer.querySelector('.field-feedback');

    fieldContainer.classList.add('error');
    fieldContainer.classList.remove('success');

    if (feedback) {
        feedback.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i>${message}`;
        feedback.className = 'field-feedback error';
    }
}

function showFieldSuccess(field, message = 'Looks good!') {
    const fieldContainer = field.closest('.form-field');
    const feedback = fieldContainer.querySelector('.field-feedback');

    fieldContainer.classList.add('success');
    fieldContainer.classList.remove('error', 'warning');

    if (feedback) {
        feedback.innerHTML = `<i class="fas fa-check-circle mr-1"></i>${message}`;
        feedback.className = 'field-feedback success';
    }
}

function showFieldWarning(field, message) {
    const fieldContainer = field.closest('.form-field');
    const feedback = fieldContainer.querySelector('.field-feedback');

    fieldContainer.classList.add('warning');
    fieldContainer.classList.remove('error', 'success');

    if (feedback) {
        feedback.innerHTML = `<i class="fas fa-exclamation-triangle mr-1"></i>${message}`;
        feedback.className = 'field-feedback warning';
    }
}

function clearFieldValidation(field) {
    const fieldContainer = field.closest('.form-field');
    const feedback = fieldContainer.querySelector('.field-feedback');

    fieldContainer.classList.remove('error', 'success', 'warning');

    if (feedback) {
        feedback.innerHTML = '';
        feedback.className = 'field-feedback';
    }
}

// Unsaved changes warning
function initializeUnsavedChangesWarning() {
    window.addEventListener('beforeunload', function(e) {
        if (hasUnsavedChanges) {
            e.preventDefault();
            e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            return e.returnValue;
        }
    });
}

// Enhanced Toast Notification System
function initializeToastSystem() {
    window.showToast = showToast;
    window.hideToast = hideToast;

    // Test toast system
    console.log('Toast system initialized');

    // Add a test button for debugging (remove in production)
    if (window.location.search.includes('debug=true')) {
        const testBtn = document.createElement('button');
        testBtn.textContent = 'Test Toast';
        testBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; padding: 10px; background: red; color: white; border: none; border-radius: 5px;';
        testBtn.onclick = () => showToast('Test toast message!', 'success');
        document.body.appendChild(testBtn);
    }
}

function showToast(message, type = 'info', duration = 5000) {
    const container = document.getElementById('toast-container');
    if (!container) return;

    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast transform transition-all duration-300 translate-x-full opacity-0`;

    const typeClasses = {
        success: 'bg-green-500 border-green-600',
        error: 'bg-red-500 border-red-600',
        warning: 'bg-yellow-500 border-yellow-600',
        info: 'bg-blue-500 border-blue-600'
    };

    const typeIcons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    toast.innerHTML = `
        <div class="flex items-center p-4 rounded-xl shadow-lg border-l-4 ${typeClasses[type]} text-white backdrop-blur-sm max-w-sm">
            <i class="${typeIcons[type]} mr-3 text-lg"></i>
            <div class="flex-1">
                <p class="font-medium font-montserrat">${message}</p>
            </div>
            <button onclick="hideToast('${toastId}')" class="ml-3 text-white hover:text-gray-200 transition-colors">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    container.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Auto hide
    if (duration > 0) {
        setTimeout(() => hideToast(toastId), duration);
    }

    return toastId;
}

function hideToast(toastId) {
    const toast = document.getElementById(toastId);
    if (!toast) return;

    toast.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
}

// Enhanced Form Response Handling
function handleFormResponse(event) {
    const xhr = event.detail.xhr;
    const saveBtn = document.getElementById('saveProfileBtn');
    const saveBtnText = saveBtn.querySelector('.save-btn-text');
    const saveBtnSpinner = saveBtn.querySelector('.save-btn-spinner');

    // Reset button state
    saveBtn.disabled = false;
    saveBtnText.textContent = 'Save Profile';
    saveBtnSpinner.classList.add('hidden');

    if (xhr.status === 200) {
        try {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                showToast('Profile updated successfully!', 'success');
                updateProfileCompletion();
                // Optionally reload page after successful save
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast(response.message || 'Error updating profile', 'error');
            }
        } catch (e) {
            // Handle HTML response (redirect)
            showToast('Profile updated successfully!', 'success');
            updateProfileCompletion();
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        }
    } else {
        showToast('Error updating profile. Please try again.', 'error');
    }
}

// Enhanced Form Submission
document.addEventListener('htmx:beforeRequest', function(event) {
    if (event.target.id === 'profileForm') {
        const saveBtn = document.getElementById('saveProfileBtn');
        const saveBtnText = saveBtn.querySelector('.save-btn-text');
        const saveBtnSpinner = saveBtn.querySelector('.save-btn-spinner');

        saveBtn.disabled = true;
        saveBtnText.textContent = 'Saving...';
        saveBtnSpinner.classList.remove('hidden');
    }
});

// Hash Navigation for Direct Tab Access
function initializeHashNavigation() {
    // Handle initial hash
    const hash = window.location.hash.substring(1);
    if (hash && document.querySelector(`[data-tab="${hash}"]`)) {
        switchToTab(hash);
    }

    // Handle hash changes
    window.addEventListener('hashchange', function() {
        const hash = window.location.hash.substring(1);
        if (hash && document.querySelector(`[data-tab="${hash}"]`)) {
            switchToTab(hash);
        }
    });
}

// Enhanced profile completion tracking
function updateProfileCompletion() {
    const form = document.getElementById('profileForm');
    if (!form) return 0;

    const completionData = calculateProfileCompletion();
    const completionPercentage = completionData.percentage;

    // Update completion ring in hero section
    updateCompletionRing(completionPercentage);

    // Update completion banner
    updateCompletionBanner(completionData);

    // Update completion tasks
    updateCompletionTasks(completionData);

    // Update hero completion value
    const heroCompletionValue = document.getElementById('heroCompletionValue');
    if (heroCompletionValue) {
        heroCompletionValue.textContent = completionPercentage + '%';
    }

    return completionPercentage;
}

function calculateProfileCompletion() {
    const form = document.getElementById('profileForm');
    if (!form) return { percentage: 0, completed: [], missing: [] };

    const fields = {
        // Essential fields (higher weight)
        essential: [
            { name: 'first_name', label: 'First Name', weight: 15 },
            { name: 'last_name', label: 'Last Name', weight: 15 },
            { name: 'email', label: 'Email Address', weight: 15 },
            { name: 'phone', label: 'Phone Number', weight: 10 }
        ],
        // Important fields (medium weight)
        important: [
            { name: 'bio', label: 'Bio/About Me', weight: 8 },
            { name: 'city', label: 'City', weight: 5 },
            { name: 'country', label: 'Country', weight: 3 },
            { name: 'date_of_birth', label: 'Date of Birth', weight: 5 }
        ],
        // Optional fields (lower weight)
        optional: [
            { name: 'gender', label: 'Gender', weight: 2 },
            { name: 'address', label: 'Address', weight: 3 },
            { name: 'secondary_phone', label: 'Secondary Phone', weight: 2 },
            { name: 'whatsapp_number', label: 'WhatsApp Number', weight: 2 },
            { name: 'preferred_language', label: 'Preferred Language', weight: 2 }
        ],
        // Special fields
        special: [
            { name: 'profile_picture', label: 'Profile Picture', weight: 8, isFile: true },
            { name: 'email_verified', label: 'Email Verification', weight: 5, isStatus: true }
        ]
    };

    let totalWeight = 0;
    let completedWeight = 0;
    const completed = [];
    const missing = [];

    // Check all field categories
    Object.keys(fields).forEach(category => {
        fields[category].forEach(fieldInfo => {
            totalWeight += fieldInfo.weight;

            let isCompleted = false;

            if (fieldInfo.isFile) {
                // Check if file field has value or existing image
                const hasExistingImage = document.querySelector(`img[src*="${fieldInfo.name}"]`) ||
                                       document.querySelector(`[alt*="profile picture"]`);
                const fileInput = form.querySelector(`[name="${fieldInfo.name}"]`);
                isCompleted = hasExistingImage || (fileInput && fileInput.files && fileInput.files.length > 0);
            } else if (fieldInfo.isStatus) {
                // Check status fields (like email verification)
                if (fieldInfo.name === 'email_verified') {
                    isCompleted = document.querySelector('.status-verified') !== null;
                }
            } else {
                // Check regular form fields
                const field = form.querySelector(`[name="${fieldInfo.name}"]`);
                isCompleted = field && field.value.trim() !== '';
            }

            if (isCompleted) {
                completedWeight += fieldInfo.weight;
                completed.push(fieldInfo);
            } else {
                missing.push(fieldInfo);
            }
        });
    });

    const percentage = Math.round((completedWeight / totalWeight) * 100);

    return {
        percentage,
        completed,
        missing,
        totalWeight,
        completedWeight
    };
}

function updateCompletionRing(percentage) {
    const completionRing = document.getElementById('profileCompletionRing');
    const completionText = document.getElementById('profileCompletionText');

    if (completionRing && completionText) {
        const circumference = 2 * Math.PI * 47; // radius = 47
        const offset = circumference - (percentage / 100) * circumference;

        completionRing.style.strokeDashoffset = offset;
        completionText.textContent = `${percentage}%`;

        // Update ring color based on completion
        if (percentage >= 80) {
            completionRing.style.stroke = '#10b981'; // Green
        } else if (percentage >= 60) {
            completionRing.style.stroke = '#f59e0b'; // Orange
        } else {
            completionRing.style.stroke = '#ef4444'; // Red
        }
    }
}

function updateCompletionBanner(completionData) {
    const banner = document.querySelector('.completion-card');
    if (!banner) return;

    const percentage = completionData.percentage;
    const progressBar = banner.querySelector('.completion-progress');
    const percentageText = banner.querySelector('.completion-percentage');

    if (progressBar) {
        progressBar.style.width = `${percentage}%`;
    }

    if (percentageText) {
        percentageText.textContent = `${percentage}%`;
    }

    // Update banner message
    const messageElement = banner.querySelector('.completion-message');
    if (messageElement) {
        let message = '';
        if (percentage >= 90) {
            message = 'Excellent! Your profile is almost complete.';
        } else if (percentage >= 70) {
            message = 'Great progress! Just a few more details needed.';
        } else if (percentage >= 50) {
            message = 'Good start! Complete more fields to improve your profile.';
        } else {
            message = 'Let\'s complete your profile to get the most out of our platform.';
        }
        messageElement.textContent = message;
    }
}

function updateCompletionTasks(completionData) {
    const tasksContainer = document.querySelector('.completion-actions');
    if (!tasksContainer) return;

    // Clear existing tasks
    tasksContainer.innerHTML = '';

    // Show top 3 missing important tasks
    const importantMissing = completionData.missing
        .filter(field => field.weight >= 5)
        .sort((a, b) => b.weight - a.weight)
        .slice(0, 3);

    importantMissing.forEach(field => {
        const taskElement = document.createElement('span');
        taskElement.className = 'completion-task';
        taskElement.innerHTML = `
            <i class="fas fa-${getFieldIcon(field.name)} mr-1"></i>
            ${field.label}
        `;
        taskElement.onclick = () => focusField(field.name);
        tasksContainer.appendChild(taskElement);
    });
}

function getFieldIcon(fieldName) {
    const icons = {
        'profile_picture': 'camera',
        'bio': 'edit',
        'phone': 'phone',
        'email': 'envelope',
        'city': 'map-marker-alt',
        'date_of_birth': 'calendar',
        'address': 'home',
        'whatsapp_number': 'whatsapp',
        'secondary_phone': 'phone-alt'
    };
    return icons[fieldName] || 'plus';
}

function focusField(fieldName) {
    const field = document.querySelector(`[name="${fieldName}"]`);
    if (field) {
        // Find which tab contains this field
        const tabContent = field.closest('.tab-content');
        if (tabContent) {
            const tabId = tabContent.id.replace('-tab', '');
            switchToTab(tabId);

            setTimeout(() => {
                field.focus();
                field.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 300);
        }
    }
}

// Toast notification system
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;

    const icon = type === 'success' ? 'check-circle' :
                 type === 'error' ? 'exclamation-circle' :
                 type === 'warning' ? 'exclamation-triangle' : 'info-circle';

    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${icon} toast-icon"></i>
            <span class="toast-message">${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add to page
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);

    // Animate in
    setTimeout(() => toast.classList.add('show'), 100);

    // Auto remove
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, duration);
}

// Enhanced Image Upload with Cropping
let currentCropper = null;
let currentImageType = 'profile'; // 'profile' or 'cover'

function handleImageUpload(input, imageType) {
    currentImageType = imageType;

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Enhanced file validation
        const validation = validateImageFile(file, imageType);
        if (!validation.valid) {
            showToast(validation.message, 'error');
            input.value = '';
            return;
        }

        // Show upload progress
        showUploadProgress(imageType, 0);

        const reader = new FileReader();
        reader.onload = function(e) {
            // Compress image before preview
            compressImage(e.target.result, imageType)
                .then(compressedImage => {
                    if (imageType === 'profile') {
                        updateProfileImagePreview(compressedImage);
                    } else {
                        previewCoverImage(compressedImage);
                    }

                    hideUploadProgress(imageType);
                    showToast('Image uploaded successfully!', 'success');
                })
                .catch(error => {
                    hideUploadProgress(imageType);
                    showToast('Error processing image', 'error');
                    input.value = '';
                });
        };

        reader.onerror = function() {
            hideUploadProgress(imageType);
            showToast('Error reading image file', 'error');
            input.value = '';
        };

        reader.readAsDataURL(file);
    }
}
                        setTimeout(() => hideUploadProgress(imageType), 1000);
                    }

                    showToast(`${imageType === 'profile' ? 'Profile' : 'Cover'} image uploaded successfully!`, 'success');
                })
                .catch(error => {
                    console.error('Image compression failed:', error);
                    showToast('Failed to process image. Please try again.', 'error');
                    hideUploadProgress(imageType);
                });
        };

        reader.onerror = function() {
            showToast('Failed to read image file. Please try again.', 'error');
            hideUploadProgress(imageType);
        };

        reader.readAsDataURL(file);
    }
}

// Enhanced image validation
function validateImageFile(file, imageType) {
    // File size validation (5MB for profile, 10MB for cover)
    const maxSize = imageType === 'profile' ? 5 * 1024 * 1024 : 10 * 1024 * 1024;
    if (file.size > maxSize) {
        return {
            valid: false,
            message: `File size too large. Maximum size is ${maxSize / (1024 * 1024)}MB.`
        };
    }

    // File type validation
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        return {
            valid: false,
            message: 'Invalid file type. Please upload JPEG, PNG, or WebP images only.'
        };
    }

    // File name validation
    if (file.name.length > 100) {
        return {
            valid: false,
            message: 'File name is too long. Please rename the file.'
        };
    }

    return { valid: true };
}

// Image compression
function compressImage(imageSrc, imageType) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = function() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Set target dimensions
            let { width, height } = getTargetDimensions(img.width, img.height, imageType);

            canvas.width = width;
            canvas.height = height;

            // Draw and compress
            ctx.drawImage(img, 0, 0, width, height);

            // Convert to blob with compression
            canvas.toBlob(
                (blob) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                },
                'image/jpeg',
                0.8 // 80% quality
            );
        };
        img.onerror = reject;
        img.src = imageSrc;
    });
}

// Get target dimensions for compression
function getTargetDimensions(originalWidth, originalHeight, imageType) {
    let maxWidth, maxHeight;

    if (imageType === 'profile') {
        maxWidth = maxHeight = 400; // Square profile images
    } else {
        maxWidth = 1200; // Cover images
        maxHeight = 400;
    }

    const ratio = Math.min(maxWidth / originalWidth, maxHeight / originalHeight);

    return {
        width: Math.round(originalWidth * ratio),
        height: Math.round(originalHeight * ratio)
    };
}

// Upload progress functions
function showUploadProgress(imageType, percentage) {
    const progressId = `${imageType}UploadProgress`;
    let progressContainer = document.getElementById(progressId);

    if (!progressContainer) {
        progressContainer = document.createElement('div');
        progressContainer.id = progressId;
        progressContainer.className = 'upload-progress-container';
        progressContainer.innerHTML = `
            <div class="upload-progress-bar">
                <div class="upload-progress-fill" style="width: 0%"></div>
            </div>
            <div class="upload-progress-text">Uploading... 0%</div>
        `;

        const targetContainer = imageType === 'profile'
            ? document.querySelector('.profile-picture-section')
            : document.querySelector('.cover-image-section');

        if (targetContainer) {
            targetContainer.appendChild(progressContainer);
        }
    }

    const progressFill = progressContainer.querySelector('.upload-progress-fill');
    const progressText = progressContainer.querySelector('.upload-progress-text');

    if (progressFill) {
        progressFill.style.width = `${percentage}%`;
    }

    if (progressText) {
        if (percentage < 100) {
            progressText.textContent = `Uploading... ${percentage}%`;
        } else {
            progressText.textContent = 'Upload complete!';
        }
    }
}

function hideUploadProgress(imageType) {
    const progressId = `${imageType}UploadProgress`;
    const progressContainer = document.getElementById(progressId);

    if (progressContainer) {
        progressContainer.style.opacity = '0';
        setTimeout(() => {
            progressContainer.remove();
        }, 300);
    }
}

function showImageUploadProgress() {
    const avatarSection = document.querySelector('.profile-avatar-section');
    if (avatarSection) {
        // Add loading overlay
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'upload-loading-overlay';
        loadingOverlay.className = 'absolute inset-0 bg-black/50 rounded-full flex items-center justify-center z-10';
        loadingOverlay.innerHTML = `
            <div class="text-center text-white">
                <div class="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent mx-auto mb-2"></div>
                <div class="text-xs font-medium">Uploading...</div>
            </div>
        `;
        avatarSection.appendChild(loadingOverlay);
    }
}

function hideImageUploadProgress() {
    const loadingOverlay = document.getElementById('upload-loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

function syncProfilePictureInputs(sourceInput) {
    // Sync the file from form input to hero input
    const heroInput = document.getElementById('profilePictureInput');
    if (heroInput && sourceInput.files && sourceInput.files[0]) {
        const dt = new DataTransfer();
        dt.items.add(sourceInput.files[0]);
        heroInput.files = dt.files;

        // Preview the image
        previewProfileImage(sourceInput);
    }
}

// Simple image validation function
function validateImageFile(file, imageType = 'profile') {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!file) {
        return { valid: false, message: 'No file selected' };
    }

    if (file.size > maxSize) {
        return { valid: false, message: 'Image size must be less than 5MB' };
    }

    if (!allowedTypes.includes(file.type)) {
        return { valid: false, message: 'Please select a valid image file (JPEG, PNG, or WebP)' };
    }

    return { valid: true, message: 'Valid image file' };
}

// Simple upload progress functions
function showUploadProgress(imageType, percentage) {
    // For now, just show the loading overlay
    if (percentage === 0) {
        showImageUploadProgress();
    }
}

function hideUploadProgress(imageType) {
    hideImageUploadProgress();
}

// Simple image compression (just return original for now)
function compressImage(imageSrc, imageType) {
    return Promise.resolve(imageSrc);
}

// Initialize clickable elements
function initializeClickableElements() {
    console.log('Initializing clickable elements...');

    // Wait for DOM to be fully ready
    setTimeout(() => {
        // Make stat cards clickable
        initializeStatCardClicks();

        // Make contact items clickable
        initializeContactItemClicks();

        // Make status badges clickable
        initializeStatusBadgeClicks();

        // Make role badges clickable
        initializeRoleBadgeClicks();

        // Add ripple effects to interactive elements
        initializeRippleEffects();

        console.log('Clickable elements initialized successfully');
    }, 500);
}

function initializeStatCardClicks() {
    const statCards = document.querySelectorAll('.stat-card');
    console.log('Found stat cards:', statCards.length);

    statCards.forEach((card, index) => {
        // Remove any existing listeners
        card.removeEventListener('click', handleStatCardClickEvent);

        // Add new listener
        card.addEventListener('click', handleStatCardClickEvent);
        console.log(`Added click listener to stat card ${index + 1}`);
    });
}

function handleStatCardClickEvent(event) {
    event.preventDefault();
    event.stopPropagation();

    const statLabel = this.querySelector('.stat-label');
    if (!statLabel) {
        console.log('No stat label found');
        return;
    }

    const statType = statLabel.textContent.toLowerCase().trim();
    console.log('Stat card clicked:', statType);
    handleStatCardClick(statType);
}

function handleStatCardClick(statType) {
    console.log('Handling stat card click for:', statType);

    switch(statType) {
        case 'complete':
            showToast('Profile completion details', 'info');
            scrollToIncompleteFields();
            break;
        case 'member since':
            showToast('You joined Gurumisha Motors!', 'info');
            break;
        case 'listings':
            showToast('Redirecting to your listings...', 'info');
            setTimeout(() => {
                try {
                    window.location.href = '/dashboard/listings/';
                } catch (e) {
                    showToast('Navigation error', 'error');
                }
            }, 1000);
            break;
        case 'orders':
            showToast('Redirecting to your orders...', 'info');
            setTimeout(() => {
                try {
                    window.location.href = '/dashboard/orders/';
                } catch (e) {
                    showToast('Navigation error', 'error');
                }
            }, 1000);
            break;
        case 'profile views':
            showToast('Redirecting to analytics...', 'info');
            setTimeout(() => {
                switchToTab('analytics');
            }, 1000);
            break;
        case 'wishlist':
            showToast('Redirecting to your wishlist...', 'info');
            setTimeout(() => {
                try {
                    window.location.href = '/dashboard/wishlist/';
                } catch (e) {
                    showToast('Navigation error', 'error');
                }
            }, 1000);
            break;
        case 'total users':
        case 'total cars':
            showToast('Admin statistics', 'info');
            break;
        default:
            showToast('Feature coming soon!', 'info');
            console.log('Unknown stat type:', statType);
    }
}

function initializeContactItemClicks() {
    const contactItems = document.querySelectorAll('.contact-item');
    console.log('Found contact items:', contactItems.length);

    contactItems.forEach((item, index) => {
        // Remove any existing listeners
        item.removeEventListener('click', handleContactItemClick);

        // Add new listener
        item.addEventListener('click', handleContactItemClick);
        console.log(`Added click listener to contact item ${index + 1}`);
    });
}

function handleContactItemClick(event) {
    event.preventDefault();
    event.stopPropagation();

    const icon = this.querySelector('i');
    const textElement = this.querySelector('span');

    if (!icon || !textElement) {
        console.log('Missing icon or text element');
        return;
    }

    const text = textElement.textContent.trim();
    console.log('Contact item clicked:', text);

    if (icon.classList.contains('fa-envelope')) {
        // Email click
        try {
            window.location.href = `mailto:${text}`;
            showToast('Opening email client...', 'info');
        } catch (e) {
            showToast('Error opening email client', 'error');
        }
    } else if (icon.classList.contains('fa-phone')) {
        // Phone click
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('Phone number copied to clipboard!', 'success');
            }).catch(() => {
                showToast('Phone: ' + text, 'info');
            });
        } else {
            showToast('Phone: ' + text, 'info');
        }
    } else if (icon.classList.contains('fa-map-marker-alt')) {
        // Location click
        try {
            const location = encodeURIComponent(text);
            window.open(`https://maps.google.com/?q=${location}`, '_blank');
            showToast('Opening location in maps...', 'info');
        } catch (e) {
            showToast('Error opening maps', 'error');
        }
    }
}

function initializeStatusBadgeClicks() {
    const statusBadges = document.querySelectorAll('.status-badge');
    console.log('Found status badges:', statusBadges.length);

    statusBadges.forEach((badge, index) => {
        badge.removeEventListener('click', handleStatusBadgeClick);
        badge.addEventListener('click', handleStatusBadgeClick);
        console.log(`Added click listener to status badge ${index + 1}`);
    });
}

function handleStatusBadgeClick(event) {
    event.preventDefault();
    event.stopPropagation();

    const text = this.textContent.trim();
    console.log('Status badge clicked:', text);

    if (text.includes('Verify Email')) {
        showToast('Email verification required', 'warning');
    } else if (text.includes('Verified')) {
        showToast('Your account is verified!', 'success');
    } else if (text.includes('Pending')) {
        showToast('Your verification is pending review', 'info');
    } else {
        showToast('Status: ' + text, 'info');
    }
}

function initializeRoleBadgeClicks() {
    const roleBadges = document.querySelectorAll('.role-badge');
    console.log('Found role badges:', roleBadges.length);

    roleBadges.forEach((badge, index) => {
        badge.removeEventListener('click', handleRoleBadgeClick);
        badge.addEventListener('click', handleRoleBadgeClick);
        console.log(`Added click listener to role badge ${index + 1}`);
    });
}

function handleRoleBadgeClick(event) {
    event.preventDefault();
    event.stopPropagation();

    const role = this.textContent.trim();
    console.log('Role badge clicked:', role);
    showToast(`You are logged in as: ${role}`, 'info');
}

function initializeRippleEffects() {
    const interactiveElements = document.querySelectorAll('.interactive-element, .stat-card, .contact-item, .nav-tab, .action-btn');
    console.log('Found interactive elements for ripple:', interactiveElements.length);

    interactiveElements.forEach((element, index) => {
        // Add ripple effect without interfering with other click handlers
        element.addEventListener('mousedown', function(e) {
            createRippleEffect(e, this);
        });
        console.log(`Added ripple effect to element ${index + 1}`);
    });
}

function createRippleEffect(event, element) {
    // Only create ripple if element has position relative or absolute
    const computedStyle = window.getComputedStyle(element);
    if (computedStyle.position === 'static') {
        element.style.position = 'relative';
    }

    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        pointer-events: none;
        animation: ripple 0.6s linear;
        z-index: 1;
    `;
    ripple.classList.add('ripple-effect');

    element.appendChild(ripple);

    setTimeout(() => {
        if (ripple.parentNode) {
            ripple.remove();
        }
    }, 600);
}

function scrollToIncompleteFields() {
    const form = document.getElementById('profileForm');
    if (!form) return;

    const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
    let firstIncomplete = null;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            if (!firstIncomplete) {
                firstIncomplete = field;
            }
            field.style.borderColor = 'var(--harrier-red)';
            field.style.boxShadow = '0 0 0 3px rgba(220, 38, 38, 0.1)';

            setTimeout(() => {
                field.style.borderColor = '';
                field.style.boxShadow = '';
            }, 3000);
        }
    });

    if (firstIncomplete) {
        firstIncomplete.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setTimeout(() => {
            firstIncomplete.focus();
        }, 500);
    }
}

function previewCoverImage(imageSrc) {
    const preview = document.getElementById('coverImagePreview');
    if (preview) {
        if (preview.tagName === 'IMG') {
            preview.src = imageSrc;
        } else {
            const img = document.createElement('img');
            img.src = imageSrc;
            img.className = 'cover-image';
            img.id = 'coverImagePreview';
            preview.parentNode.replaceChild(img, preview);
        }
    }
}

function openImageCropper(imageSrc = null) {
    const modal = document.getElementById('imageCropperModal');
    const cropperImage = document.getElementById('cropperImage');

    if (!imageSrc) {
        const preview = document.getElementById('profileImagePreview');
        if (preview && preview.tagName === 'IMG') {
            imageSrc = preview.src;
        } else {
            alert('Please upload an image first.');
            return;
        }
    }

    cropperImage.src = imageSrc;
    cropperImage.style.display = 'block';
    modal.classList.remove('hidden');

    // Initialize Cropper.js (you'll need to include the library)
    setTimeout(() => {
        if (window.Cropper) {
            currentCropper = new Cropper(cropperImage, {
                aspectRatio: 1,
                viewMode: 1,
                dragMode: 'move',
                autoCropArea: 0.8,
                restore: false,
                guides: true,
                center: true,
                highlight: false,
                cropBoxMovable: true,
                cropBoxResizable: true,
                toggleDragModeOnDblclick: false,
                preview: '#cropperPreviewCircle, #cropperPreviewSquare'
            });
        }
    }, 100);
}

function closeImageCropper() {
    const modal = document.getElementById('imageCropperModal');
    modal.classList.add('hidden');

    if (currentCropper) {
        currentCropper.destroy();
        currentCropper = null;
    }
}

function setAspectRatio(ratio) {
    if (currentCropper) {
        currentCropper.setAspectRatio(ratio);
    }

    // Update active button
    document.querySelectorAll('.aspect-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

function rotateCropper(degrees) {
    if (currentCropper) {
        currentCropper.rotate(degrees);
    }
}

function flipCropper(direction) {
    if (currentCropper) {
        if (direction === 'horizontal') {
            currentCropper.scaleX(-currentCropper.getData().scaleX || -1);
        } else {
            currentCropper.scaleY(-currentCropper.getData().scaleY || -1);
        }
    }
}

function applyCrop() {
    if (!currentCropper) return;

    const canvas = currentCropper.getCroppedCanvas({
        width: currentImageType === 'profile' ? 400 : 1200,
        height: currentImageType === 'profile' ? 400 : 400,
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high'
    });

    const croppedImageSrc = canvas.toDataURL('image/jpeg', 0.9);

    if (currentImageType === 'profile') {
        previewProfileImage(croppedImageSrc);
    } else {
        previewCoverImage(croppedImageSrc);
    }

    // Convert to blob and update file input
    canvas.toBlob(function(blob) {
        const file = new File([blob], `${currentImageType}_image.jpg`, {
            type: 'image/jpeg',
            lastModified: Date.now()
        });

        // Update the file input
        const input = document.getElementById(currentImageType === 'profile' ? 'profilePictureInput' : 'coverImageInput');
        if (input) {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            input.files = dataTransfer.files;
        }
    }, 'image/jpeg', 0.9);

    closeImageCropper();
}

function removeCoverImage() {
    const preview = document.getElementById('coverImagePreview');
    const input = document.getElementById('coverImageInput');

    if (preview && input) {
        input.value = '';

        if (preview.tagName === 'IMG') {
            const placeholder = document.createElement('div');
            placeholder.className = 'cover-placeholder';
            placeholder.id = 'coverImagePreview';
            placeholder.innerHTML = `
                <i class="fas fa-image text-6xl text-gray-400 mb-4"></i>
                <p class="text-gray-500 text-lg font-medium">Add a cover image</p>
                <p class="text-gray-400 text-sm">Showcase your business with a beautiful cover photo</p>
            `;
            preview.parentNode.replaceChild(placeholder, preview);
        }
    }
}

// Image optimization and compression
function compressImage(file, maxWidth = 1200, quality = 0.8) {
    return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = function() {
            const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
            canvas.width = img.width * ratio;
            canvas.height = img.height * ratio;

            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

            canvas.toBlob(resolve, 'image/jpeg', quality);
        };

        img.src = URL.createObjectURL(file);
    });
}

// Upload progress simulation
function showUploadProgress() {
    const modal = document.getElementById('uploadProgressModal');
    const progressFill = document.getElementById('uploadProgressFill');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');

    modal.classList.remove('hidden');

    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;

        progressFill.style.width = progress + '%';
        progressPercent.textContent = Math.round(progress) + '%';

        if (progress < 30) {
            progressText.textContent = 'Preparing upload...';
        } else if (progress < 70) {
            progressText.textContent = 'Uploading image...';
        } else if (progress < 95) {
            progressText.textContent = 'Processing image...';
        } else {
            progressText.textContent = 'Finalizing...';
        }

        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                modal.classList.add('hidden');
                progressFill.style.width = '0%';
                progressPercent.textContent = '0%';
                progressText.textContent = 'Preparing upload...';
            }, 500);
        }
    }, 100);
}
</script>

<!-- Image Cropper Modal -->
<div id="imageCropperModal" class="modal-overlay hidden">
    <div class="modal-container image-cropper-modal">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-crop mr-2"></i>
                Crop Your Image
            </h3>
            <button type="button" class="modal-close" onclick="closeImageCropper()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
            <div class="cropper-container">
                <div class="cropper-preview-area">
                    <img id="cropperImage" src="" alt="Image to crop" style="max-width: 100%; display: none;">
                </div>

                <div class="cropper-controls">
                    <div class="cropper-options">
                        <div class="aspect-ratio-buttons">
                            <button type="button" class="aspect-btn active" data-aspect="1" onclick="setAspectRatio(1)">
                                <i class="fas fa-square mr-1"></i>Square (1:1)
                            </button>
                            <button type="button" class="aspect-btn" data-aspect="1.5" onclick="setAspectRatio(1.5)">
                                <i class="fas fa-rectangle-landscape mr-1"></i>Portrait (2:3)
                            </button>
                            <button type="button" class="aspect-btn" data-aspect="0.75" onclick="setAspectRatio(0.75)">
                                <i class="fas fa-rectangle-portrait mr-1"></i>Landscape (3:2)
                            </button>
                            <button type="button" class="aspect-btn" data-aspect="NaN" onclick="setAspectRatio(NaN)">
                                <i class="fas fa-expand mr-1"></i>Free
                            </button>
                        </div>

                        <div class="cropper-actions">
                            <button type="button" class="cropper-action-btn" onclick="rotateCropper(-90)">
                                <i class="fas fa-undo mr-1"></i>Rotate Left
                            </button>
                            <button type="button" class="cropper-action-btn" onclick="rotateCropper(90)">
                                <i class="fas fa-redo mr-1"></i>Rotate Right
                            </button>
                            <button type="button" class="cropper-action-btn" onclick="flipCropper('horizontal')">
                                <i class="fas fa-arrows-alt-h mr-1"></i>Flip H
                            </button>
                            <button type="button" class="cropper-action-btn" onclick="flipCropper('vertical')">
                                <i class="fas fa-arrows-alt-v mr-1"></i>Flip V
                            </button>
                        </div>
                    </div>

                    <div class="cropper-preview">
                        <h4 class="preview-title">Preview</h4>
                        <div class="preview-container">
                            <div class="preview-circle" id="cropperPreviewCircle"></div>
                            <div class="preview-square" id="cropperPreviewSquare"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeImageCropper()">
                <i class="fas fa-times mr-2"></i>Cancel
            </button>
            <button type="button" class="btn btn-primary" onclick="applyCrop()">
                <i class="fas fa-check mr-2"></i>Apply Crop
            </button>
        </div>
    </div>
</div>

<!-- Image Upload Progress Modal -->
<div id="uploadProgressModal" class="modal-overlay hidden">
    <div class="modal-container upload-progress-modal">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-cloud-upload-alt mr-2"></i>
                Uploading Image
            </h3>
        </div>

        <div class="modal-body">
            <div class="upload-progress-container">
                <div class="upload-progress-bar">
                    <div class="upload-progress-fill" id="uploadProgressFill"></div>
                </div>
                <div class="upload-progress-text">
                    <span id="uploadProgressText">Preparing upload...</span>
                    <span id="uploadProgressPercent">0%</span>
                </div>
                <div class="upload-status-icon">
                    <i class="fas fa-spinner fa-spin text-harrier-blue text-2xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>
</script>
{% endblock %}

{% block extra_js %}
<!-- Cropper.js for image cropping functionality -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js" integrity="sha512-ZK6m9vkdZHEBOKHOLaMCWDvdV6fGD9BbNrFzAJqwJdqjzrWxdueKGBPe9B6v6nZjBFCrRABdNbRjlZiEinf3jQ==" crossorigin="anonymous" referrerpolicy="no-referrer" defer></script>

<!-- Performance monitoring and optimization -->
<script>
// Performance monitoring
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                console.log('Page Load Performance:', {
                    'DNS Lookup': perfData.domainLookupEnd - perfData.domainLookupStart,
                    'TCP Connection': perfData.connectEnd - perfData.connectStart,
                    'Request': perfData.responseStart - perfData.requestStart,
                    'Response': perfData.responseEnd - perfData.responseStart,
                    'DOM Processing': perfData.domContentLoadedEventEnd - perfData.responseEnd,
                    'Total Load Time': perfData.loadEventEnd - perfData.navigationStart
                });
            }
        }, 0);
    });
}

// Intersection Observer for lazy loading animations
if ('IntersectionObserver' in window) {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all animatable elements
    document.addEventListener('DOMContentLoaded', function() {
        const animatableElements = document.querySelectorAll('.form-section, .analytics-card, .performance-item');
        animatableElements.forEach(function(el) {
            observer.observe(el);
        });
    });
}

// Accessibility enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add ARIA labels for screen readers
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(function(button, index) {
        button.setAttribute('aria-describedby', 'tab-description-' + index);
        button.setAttribute('role', 'tab');
    });

    // Add keyboard navigation for tabs
    tabButtons.forEach(function(button, index) {
        button.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
                e.preventDefault();
                const nextIndex = e.key === 'ArrowRight'
                    ? (index + 1) % tabButtons.length
                    : (index - 1 + tabButtons.length) % tabButtons.length;
                tabButtons[nextIndex].focus();
                tabButtons[nextIndex].click();
            }
        });
    });

    // Enhanced focus management
    const focusableElements = document.querySelectorAll('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
    focusableElements.forEach(function(el) {
        el.addEventListener('focus', function() {
            this.classList.add('focus-visible');
        });
        el.addEventListener('blur', function() {
            this.classList.remove('focus-visible');
        });
    });

    // Announce dynamic content changes to screen readers
    function announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;
        document.body.appendChild(announcement);
        setTimeout(function() {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Make announcements available globally
    window.announceToScreenReader = announceToScreenReader;
});

// Progressive enhancement for older browsers
if (!window.fetch) {
    console.warn('Fetch API not supported. Some features may be limited.');
}

if (!window.IntersectionObserver) {
    document.addEventListener('DOMContentLoaded', function() {
        const animatableElements = document.querySelectorAll('.form-section, .analytics-card, .performance-item');
        animatableElements.forEach(function(el) {
            el.classList.add('animate-in');
        });
    });
}
</script>
{% endblock %}
